#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
#
Pod::Spec.new do |s|
  s.name             = 'aiclipper_whisper'
  s.version          = '0.0.1'
  s.summary          = 'A Flutter plugin for whisper speech recognition.'
  s.description      = <<-DESC
A Flutter plugin for whisper speech recognition with FFI support.
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files     = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '15.0'
  s.swift_version = '5.0'

  # Use vendored_frameworks from local iOS directory
  s.vendored_frameworks = 'whisper.xcframework'
  
  s.pod_target_xcconfig = {
    'DEFINES_MODULE' => 'YES',
    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++17',
    'GCC_PREPROCESSOR_DEFINITIONS' => 'GGML_USE_ACCELERATE=1 GGML_USE_METAL=1',
    'OTHER_LDFLAGS' => '-lwhisper',
    # Use local xcframework paths
    'HEADER_SEARCH_PATHS' => '$(PODS_TARGET_SRCROOT)/whisper.xcframework/ios-arm64/Headers $(PODS_TARGET_SRCROOT)/whisper.xcframework/ios-arm64-simulator/Headers',
    'LIBRARY_SEARCH_PATHS' => '$(PODS_TARGET_SRCROOT)/whisper.xcframework/ios-arm64 $(PODS_TARGET_SRCROOT)/whisper.xcframework/ios-arm64-simulator'
  }
  
  # Force the main app to load all symbols from this static library
  s.user_target_xcconfig = {
    'OTHER_LDFLAGS' => '$(inherited) -all_load'
  }
  
  s.frameworks = 'Accelerate', 'Metal', 'Foundation', 'AVFoundation'
end 