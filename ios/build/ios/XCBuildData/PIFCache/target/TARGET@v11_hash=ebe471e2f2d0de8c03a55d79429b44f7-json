{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d9c8ee1557b5fc791addbbe9df1c11a4", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b4a019b534ee14a9ed6614ffef44cb22", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989213307a3df7744317618703944f5e9a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980791c35b6e5a7fa6e68412ff6b087a1f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989213307a3df7744317618703944f5e9a", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98614731b7be4bf293df8ec98d0c3e5c89", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f6e4f3fa62708644f88949e07b9bae90", "guid": "bfdfe7dc352907fc980b868725387e982468a8571055d813ae7ea129789b9eaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984350aaf2de0b7955678051f2c5367c49", "guid": "bfdfe7dc352907fc980b868725387e9878e0d173ccf8d166972f0ea89ef42028", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd99b510707902918fd8da70bc5d5d49", "guid": "bfdfe7dc352907fc980b868725387e98485c1be0a2851194774ed334f64f0bd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98363b84d0a3b2c68e62ac274bbd39b7c8", "guid": "bfdfe7dc352907fc980b868725387e986c7240c87cab6b2fda1814084dc6bbbc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98655f0c7bf1e502d69619ca1bfe2f6371", "guid": "bfdfe7dc352907fc980b868725387e98ae8aa4a0c1648fa8ce62d8ddfe6e5073", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fe663bf190ab2bb1418d9997fca98ed", "guid": "bfdfe7dc352907fc980b868725387e988c8846d352eb6e4379989bb3599b40d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf8136ddc067b9cfe6fad47571e47539", "guid": "bfdfe7dc352907fc980b868725387e986d845023689dd5251e6aeb6792eb942b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892060032de65233fe1c1d48badec15a9", "guid": "bfdfe7dc352907fc980b868725387e98c35faf90ed2b843aac871662267dc078", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804266655346caa80a03a9201c69e6712", "guid": "bfdfe7dc352907fc980b868725387e9878460ce5e9487d0cc788c2a75b213ea9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e037ee3ec25598a42a6f84c50d108ad", "guid": "bfdfe7dc352907fc980b868725387e98ab3c0999a49577815c1f0df4827e53cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fbb020fcea70163ef9e18c89d7828c7", "guid": "bfdfe7dc352907fc980b868725387e984e45ab15bba982f45e1aa8697dbe3f45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d49bbb293c382407447bdc8ef84e5e05", "guid": "bfdfe7dc352907fc980b868725387e9811c0acd30f99737d63e6498088e951a0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894c923a755a19557d20c58919d796d9c", "guid": "bfdfe7dc352907fc980b868725387e987f383cb3c74096ed19fb00992ea7e9ce", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbcff641266f133994067dba163eb82c", "guid": "bfdfe7dc352907fc980b868725387e9819964d2cfd92defd3a8da645ff2005aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851c9cebf512cfa95d2f65f902719d5a2", "guid": "bfdfe7dc352907fc980b868725387e98b21baccbc0de67d939e2f9b402eb8c39", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987677530c4c5b07d529f714ce776f67c8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9899ecacc279ebaea264c0c4ec47eeb31c", "guid": "bfdfe7dc352907fc980b868725387e98d5dc98f237ab47aa95de569671da8532"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27038e7adfaaafd017517ff605fd063", "guid": "bfdfe7dc352907fc980b868725387e980152d0b6bcffb32985c899390e41ee48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f34a0df0b51da10f5f2c97ceeaba3df1", "guid": "bfdfe7dc352907fc980b868725387e9814ada7d043972518c9a93b4c0a2fe138"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e589e42938c097f4ad4a8e6db74de25", "guid": "bfdfe7dc352907fc980b868725387e9839960954bbfb2e88b1eaf9a7fdc4b06c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a4d03877b25bc36535723a500b6ff9c", "guid": "bfdfe7dc352907fc980b868725387e981c770b2e27b823f843504d1840129684"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cbd39d2451c97da4c26bcd0020b965a", "guid": "bfdfe7dc352907fc980b868725387e987da063feb95775ace0b324d2b1200a42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981211670cacd4f1259343b0a301c8c230", "guid": "bfdfe7dc352907fc980b868725387e983ea1af8b47fd59d283355361e452eeaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdfc1245247902c81a4fc3ff8af31da8", "guid": "bfdfe7dc352907fc980b868725387e988dc7cb1cf782fe7bca13f81841b5b115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980198b60da51313c245a1bf25e6c77ce6", "guid": "bfdfe7dc352907fc980b868725387e98abe603433d2c051ded3cad5f94732c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a90679f478d3758c81be4468bc7d39", "guid": "bfdfe7dc352907fc980b868725387e9809418166e50f1e44cfd762397fc5d091"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd685f17fee52ec1e6fffe0c18448524", "guid": "bfdfe7dc352907fc980b868725387e9892bb2e1c10d694da23ca0e320a5ba998"}], "guid": "bfdfe7dc352907fc980b868725387e983bb6061290f92dea8fbccf980aa93fc8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e982d33478867688ae1c472b14973a97b6c"}], "guid": "bfdfe7dc352907fc980b868725387e98f47adde5f8fb8910579ae4555f1007a2", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98969c8f04df025911f0cfdf7d28696110", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}