{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae529f76a1aeb84aed676cbc438f2337", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c18a2ff295014be45901fadc0a25e1a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9845285c76b70110f4302bf40b1d794a6c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987c0ad1a68339e983c25e4de21965f4a8", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981021a88a57cf3a57b1171bdb1a8457fb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e61c3706c4c5522b7ecd10e962efb922", "guid": "bfdfe7dc352907fc980b868725387e983027514cacc361ea7d7768f3a00e0ba1", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989f3cac6461b3712ef67a3db1a6ba3cc2", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986cd8b63f1efadf20d9ea1ed8f5a55c59", "guid": "bfdfe7dc352907fc980b868725387e985598a969d22d59e768569fd7ead4816b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d637eecdfef95bbdf30369120352bd46", "guid": "bfdfe7dc352907fc980b868725387e9880dc24b85e3ae54ae0a37f90507151d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834be2bf990d387cbce4913d90fc0866d", "guid": "bfdfe7dc352907fc980b868725387e98feff14f6be15a052724519ff671981c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984767f7ebe824e56e6af065bd8c127fb1", "guid": "bfdfe7dc352907fc980b868725387e981ec5bd92222e865fa8689bf4649ab87a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98051e74ac2dfb008bbd683e30491aa7be", "guid": "bfdfe7dc352907fc980b868725387e9871c18babaaae5d659d983cdc169b6623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982675e10da08f11651d5e552f26200541", "guid": "bfdfe7dc352907fc980b868725387e982dfaeeb4f8e5593d5363c86cc9599962"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f1d1fb0dd3d6538989f7502ec1644c", "guid": "bfdfe7dc352907fc980b868725387e98afd6900c37c962fa9ebc7829e030d849"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825db5c0b0de22f7488309691c8541b75", "guid": "bfdfe7dc352907fc980b868725387e980a567979fc18f0b89366654b5f43c4a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa0b02535812fd0e603a89bcfb30ef5", "guid": "bfdfe7dc352907fc980b868725387e9894e1c28b967e71083b904c59503d9e9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b13723b07b108638ef45e70068037a0f", "guid": "bfdfe7dc352907fc980b868725387e983b7376bd16b4eeffe004f3cbcd5cf307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aecacec943b3b7fb13ca4f783aeb124", "guid": "bfdfe7dc352907fc980b868725387e98956d940c1e53ef8d2d0bd5b9180ca33c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982683856913254da4512b6e02f6e4f14f", "guid": "bfdfe7dc352907fc980b868725387e989aa7f993e6562a7960fdba2f89a72149"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9552c70edc7387c0167f655a2f9cab9", "guid": "bfdfe7dc352907fc980b868725387e98b7697b7cc8851dfbe9db7114e5100f1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0333415fb47d981a93980d7783a25bc", "guid": "bfdfe7dc352907fc980b868725387e98c3a71b8cb31bfdc127ed5a40ddc83df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864c787672805f0b55134cb199dc71c0e", "guid": "bfdfe7dc352907fc980b868725387e9804023b8d34ae4ada08b9b69c30786021"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98850d9e8cfb22b221e8e383807ea38a07", "guid": "bfdfe7dc352907fc980b868725387e980ff08475b3c747ef58f0268302b40e13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5b79d94ff93ec63566064eb357dbe2c", "guid": "bfdfe7dc352907fc980b868725387e988dec29239b2ddc0bd1ce7d2f7816cc18"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830b4739ebde111a52547b4d18a503410", "guid": "bfdfe7dc352907fc980b868725387e98a96d575fae0e82c1a8e330149145f91b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2693191bcef035b93392dabd29e915", "guid": "bfdfe7dc352907fc980b868725387e98fc24b8b65d160a7e5a611169d52b0c24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7133de307e021954fb1ea816636ae53", "guid": "bfdfe7dc352907fc980b868725387e98115bdecca5c57bc92779bb2e5641003a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817c885c72c438eb48d3c624d21c32879", "guid": "bfdfe7dc352907fc980b868725387e9884a821f46517d491718a951cb2e473a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986504caedde90e4b7bbf90bc3c93b56f5", "guid": "bfdfe7dc352907fc980b868725387e9899833fd714e272f3e7cce566dd967d90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98119755cf3c3ab367d61b1f6a2b919fbf", "guid": "bfdfe7dc352907fc980b868725387e98979a36dc08da6fc1266c1eed5e0fe60d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcde9c1fdc32784294392718f0d45ce0", "guid": "bfdfe7dc352907fc980b868725387e98c7e98398300ad1d48e3e0ab529f1ade2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e630bb3219120e5c9a7785f505fc0eba", "guid": "bfdfe7dc352907fc980b868725387e986f85f7efbd5c684d65a6d24232752882"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3eb35d57bc96b724d16fb6a815089bd", "guid": "bfdfe7dc352907fc980b868725387e989a7cec12bc77464f9b4e4cd4829d3298"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98807401bfb1caf2d2d87935ce9df53b6e", "guid": "bfdfe7dc352907fc980b868725387e98b4ef2e325ed5d7c2b328c871ef22ad81"}], "guid": "bfdfe7dc352907fc980b868725387e987c45298dedbd432b971098a5ff6fcf93", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e98d6a6f6b80e98cd4223ddbe955113e8f8"}], "guid": "bfdfe7dc352907fc980b868725387e982da1defe1985d063fb3a7cb2bb833621", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987d8963b4c679eaf457b684edf7636b0d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}