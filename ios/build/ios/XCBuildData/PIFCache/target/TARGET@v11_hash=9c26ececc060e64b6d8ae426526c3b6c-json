{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983306e51079cab57766481423d5cbc609", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/share_plus", "ENABLE_MODULE_VERIFIER": "YES", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "share_plus", "INFOPLIST_FILE": "Target Support Files/share_plus/ResourceBundle-share_plus_privacy-share_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS": "gnu11 gnu++14", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "share_plus_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984eb45e13f48a3d42b0fa54d221a58cdb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9802e63deaac6aa23c75974333ed58a92d", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/share_plus", "ENABLE_MODULE_VERIFIER": "YES", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "share_plus", "INFOPLIST_FILE": "Target Support Files/share_plus/ResourceBundle-share_plus_privacy-share_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS": "gnu11 gnu++14", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "share_plus_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987b572f92d447c2c67f58f2980671539b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9802e63deaac6aa23c75974333ed58a92d", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/share_plus", "ENABLE_MODULE_VERIFIER": "YES", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "share_plus", "INFOPLIST_FILE": "Target Support Files/share_plus/ResourceBundle-share_plus_privacy-share_plus-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULE_VERIFIER_SUPPORTED_LANGUAGE_STANDARDS": "gnu11 gnu++14", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "share_plus_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9878428f52ab3198906f6383d89a58a084", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9843d286bf4adca6b6f9551d9934ac5945", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981313a3a007c29731a17f2870936effc4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c41c286d13ecd0e072787f505369f65c", "guid": "bfdfe7dc352907fc980b868725387e9889106ae3533f6c33dafbfd1a3079230b"}], "guid": "bfdfe7dc352907fc980b868725387e989a62713c2a66297c4f02e5a1888d38ae", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98de00f90750e7753637464fe34137709d", "name": "share_plus-share_plus_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e987f86a96a3ca03f6247aa68a7b2c0bfd0", "name": "share_plus_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}