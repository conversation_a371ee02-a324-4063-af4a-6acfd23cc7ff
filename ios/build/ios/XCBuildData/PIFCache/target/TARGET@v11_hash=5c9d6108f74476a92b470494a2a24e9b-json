{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e627b81f2277a26392e82dc437b640b9", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9820687528540fcc3b11d988de5572bb5e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d75277213882af2229bf750e18aa72b8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKImagePickerController/DKImagePickerController-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/DKImagePickerController/DKImagePickerController.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKImagePickerController", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98314bfb1c68a0465938714a08b439ae9a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985ab2e42ee779365312a2e5508ae2de1e", "guid": "bfdfe7dc352907fc980b868725387e984e43859c3176df0edd139f5796ab69e8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980629c617d95ce6b7724ac581dbd16a2d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988f763bf4ea79de6aab5b94580ae10b71", "guid": "bfdfe7dc352907fc980b868725387e98da78c8ed0292abf8b4d17417c21877ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cc10f8287258333398e1ad06a4a08a3", "guid": "bfdfe7dc352907fc980b868725387e98ce83fba9fe862cb673577a3144a2a43f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca1aaf1416f26af2ae5f25634becd9c4", "guid": "bfdfe7dc352907fc980b868725387e988faee52b692fb3c7d05a8a2a070990c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b730b9fab9540947d4efc3914a5285a", "guid": "bfdfe7dc352907fc980b868725387e98ffbdd3a6e319e4a71603b6bc605803e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833d01cea44331bda1c800eda7b935ca0", "guid": "bfdfe7dc352907fc980b868725387e98c880c8eb6e262d0d00a94c377859a5ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98512c98536ba573a67ee198b5633d1e06", "guid": "bfdfe7dc352907fc980b868725387e98b44f6d10cdbf303f0728ff79d4d6df0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982209b7553c41bc450d59de2600e4d0ba", "guid": "bfdfe7dc352907fc980b868725387e98b49fdb03ce9122d0f4c0c263588f463b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809ba972982903cd370c2830b507f7cdc", "guid": "bfdfe7dc352907fc980b868725387e985ba49b135d459f3404ee03f0715a7c0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989527d8513b31207fb5df20aae2636849", "guid": "bfdfe7dc352907fc980b868725387e98367fbe9cfec0632da28fc407901a8667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6330c9c6e21a4664f5beae366c00a92", "guid": "bfdfe7dc352907fc980b868725387e985902fdd1d3aba354d788271e0d5ca345"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895da6c6373e615c51e15fcb4aeeb73df", "guid": "bfdfe7dc352907fc980b868725387e9879ab47377de259541315bd9bc9e8507a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0adc46b93f26ecabb549d2d494ef03f", "guid": "bfdfe7dc352907fc980b868725387e983646b6ba14224786f6ca0a1cb190e392"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7af9bfb8e10345c1a1c53f585c3c04c", "guid": "bfdfe7dc352907fc980b868725387e98f2c68bf083510f4ae5208428b2693093"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dd48177b19451cf5bb2e0df6e574be0", "guid": "bfdfe7dc352907fc980b868725387e984991ab154a2950a20e5c7b5ee3bf7432"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98995650deb78ce16a63a4e17b69f74325", "guid": "bfdfe7dc352907fc980b868725387e98a7dd789d0339de7356dd9ee1223b64a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f83eb8f1827962c624c40e4e7e70882", "guid": "bfdfe7dc352907fc980b868725387e987d769464fda79d46f222c95d883d94c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98343fe56404bd760cbcc263e8b9fdd9cd", "guid": "bfdfe7dc352907fc980b868725387e98f7e0a19fd1b52795bf6737350c3e44cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c9340f0d7ca2817eb5340d37b215ec2", "guid": "bfdfe7dc352907fc980b868725387e98ed159288b1d5545ceec299c11b0bb6ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f599ce34b9c5181fb8b30a916d4c1d0", "guid": "bfdfe7dc352907fc980b868725387e98d05f200df89a244bd781a527282836f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867fcaca65682c24521cf1614ac18eba0", "guid": "bfdfe7dc352907fc980b868725387e988220225d4fcb04a6940eafd5f01a9e29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c17123e03ebe0c296d573c6f10fb58e5", "guid": "bfdfe7dc352907fc980b868725387e98da6456169e4c291a612d91021676f19e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985891ab7d7e3827776ba393adc0b4b906", "guid": "bfdfe7dc352907fc980b868725387e98ec17e8938e206d4b120c3007d2fcb09e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98383f6f8bf116a9145a72ca004f627102", "guid": "bfdfe7dc352907fc980b868725387e98526d12c8471effe7f3d63067b6ba9246"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f21122349a140b585d33ef23e0d9b181", "guid": "bfdfe7dc352907fc980b868725387e9898bc1da859aa4339c2b950453b2bbfb7"}], "guid": "bfdfe7dc352907fc980b868725387e98c045d3b6b2528078d538ae27a473aac2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e9869a3de14115130e15553507b0fa6534c"}], "guid": "bfdfe7dc352907fc980b868725387e98e476ae5a8e86df2901b6ff84b5fb129c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fe46abae1c24782ec0c44af58f993293", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController"}, {"guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery"}], "guid": "bfdfe7dc352907fc980b868725387e985fd5cdb9993b1816141f0c012ffa62bd", "name": "DKImagePickerController", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f752ba05adf197c3696519e901961310", "name": "DKImagePickerController.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}