{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e627b81f2277a26392e82dc437b640b9", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9823844c27f39aee6ff6d3d2f8f1003984", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987aa02ddb6d5db768edd840921c684fb0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"BUILD_LIBRARY_FOR_DISTRIBUTION": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9805ad4691e0ff5351170a44841d177e2b", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9806972f785014a1f402bb5703ea4cea2e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983a1b569bc2f81bc21e461e63eaa083b7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a292699c7027eaa680bfe5da607e708f", "guid": "bfdfe7dc352907fc980b868725387e98c31ad2d5d73bf5b1e9b03dd2a9b265bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b8cf46dbb173cb75a3b48bbfbb8858", "guid": "bfdfe7dc352907fc980b868725387e9805d8716b3b027199edfec9b6dec4dfcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835648ce799d62afd8c7500844a76b60e", "guid": "bfdfe7dc352907fc980b868725387e98343ad67d35c1169a4660de23fba0a0b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff058d7b390b6f2003ce67e8d91cd71", "guid": "bfdfe7dc352907fc980b868725387e98350db3c3e4e84b94d94625e2c686d427"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defb1e32e9bd8aba19d9a67f858e60b0", "guid": "bfdfe7dc352907fc980b868725387e98192feb192a701ae857ad508a1fce7fb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a0a427f1edd3500994a26f3b1b4614", "guid": "bfdfe7dc352907fc980b868725387e9874cccd018bdfea9fb070f526fc073d5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a490e75903fb2b0a8fb899a90d4287ed", "guid": "bfdfe7dc352907fc980b868725387e985860a8c22c10e3b575805d8af3d89f0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c76b719348323ba9f42f0d81d70b161", "guid": "bfdfe7dc352907fc980b868725387e98ca484afa3a1a8ec94ad4c25ceabeabe5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980962a367e6152e96b70a125d815020af", "guid": "bfdfe7dc352907fc980b868725387e985e42eedd237aab666031f113481888e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a11f800dc26d56426f6b37f8a283b0b", "guid": "bfdfe7dc352907fc980b868725387e98f9a97278f17bbd302ef32110a36889fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08b2c243127aba3d1fc2398f7ba08f3", "guid": "bfdfe7dc352907fc980b868725387e98c7b8a6123c8e271e021da523fef22670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989796f8ce5b602046976a913145a66f6c", "guid": "bfdfe7dc352907fc980b868725387e983575547d2d207894c215dd4c5ac294d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986968906746efa810a96cf74bfa70670d", "guid": "bfdfe7dc352907fc980b868725387e985138b31a4ba222ab09c23f21f4b3d08b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98936c99140c6d493cc2084861c68133a2", "guid": "bfdfe7dc352907fc980b868725387e981b68e8761e55acd101ae89b3a74a832e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9017cb198b86bc9ed0cf016c9d298fc", "guid": "bfdfe7dc352907fc980b868725387e982c59d1255761b95eaf3f9a9f7062e034"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135e59325df8adc673d9b81eb08333fb", "guid": "bfdfe7dc352907fc980b868725387e9897d512bf2a76cbcc0d36c47ae3921719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc8787438970f71c64e1d428d742b0c", "guid": "bfdfe7dc352907fc980b868725387e984a97d1fd5cad83e6c28af1a348e17f37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878653648422e683002192a6134cd98ad", "guid": "bfdfe7dc352907fc980b868725387e989d9a523bf29dad527f2461f9b4784b34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c46c4af4d494babb82844bf0d68a4f4", "guid": "bfdfe7dc352907fc980b868725387e98af0baa33d7408d157b3f1244e4fa603f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbca80ed18432ed6f1cbccf6775b30bb", "guid": "bfdfe7dc352907fc980b868725387e9820edc133f48e81bf4f290f37b32217a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e46671a6db2c214e25ea65b3d2809b", "guid": "bfdfe7dc352907fc980b868725387e98985f39b96bbded2a597fbbc6fa4ffa8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7b40e8c8c2cda51e62b5c5e2d2129be", "guid": "bfdfe7dc352907fc980b868725387e9861663599370726660e4413352ec6bed9"}], "guid": "bfdfe7dc352907fc980b868725387e9854cce830487eb47f36581ca8d4c127c3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}