PODS:
  - aiclipper_whisper (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - opencv_ffi (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - share_plus (0.0.1):
    - Flutter
  - SwiftyGif (5.4.5)
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - visionkit (0.0.1):
    - Flutter

DEPENDENCIES:
  - aiclipper_whisper (from `.symlinks/plugins/aiclipper_whisper/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - opencv_ffi (from `.symlinks/plugins/opencv_ffi/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - visionkit (from `.symlinks/plugins/visionkit/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - SDWebImage
    - SwiftyGif

EXTERNAL SOURCES:
  aiclipper_whisper:
    :path: ".symlinks/plugins/aiclipper_whisper/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  opencv_ffi:
    :path: ".symlinks/plugins/opencv_ffi/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  visionkit:
    :path: ".symlinks/plugins/visionkit/ios"

SPEC CHECKSUMS:
  aiclipper_whisper: 95919f5a9f8719888b28ddcb8c875b60f5c58695
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Flutter: cabc95a1d2626b1b06e7179b784ebcf0c0cde467
  opencv_ffi: a0cc19eec55d68de60efe1f43ccfb6dbe53820c4
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  share_plus: 50da8cb520a8f0f65671c6c6a99b3617ed10a58a
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  visionkit: 3d3c1246fa26190fb41935eafe1b802f84c67913

PODFILE CHECKSUM: 05dbb60992fc93d65a598937a73e5a752bd24872

COCOAPODS: 1.16.2
