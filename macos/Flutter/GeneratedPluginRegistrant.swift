//
//  Generated file. Do not edit.
//

import FlutterMacOS
import Foundation

import aiclipper_whisper
import file_picker
import mediapipe
import path_provider_foundation
import share_plus
import video_player_avfoundation
import visionkit

func RegisterGeneratedPlugins(registry: FlutterPluginRegistry) {
  AiclipperWhisperPlugin.register(with: registry.registrar(forPlugin: "AiclipperWhisperPlugin"))
  FilePickerPlugin.register(with: registry.registrar(forPlugin: "FilePickerPlugin"))
  MediapipePlugin.register(with: registry.registrar(forPlugin: "MediapipePlugin"))
  PathProviderPlugin.register(with: registry.registrar(forPlugin: "PathProviderPlugin"))
  SharePlusMacosPlugin.register(with: registry.registrar(forPlugin: "SharePlusMacosPlugin"))
  FVPVideoPlayerPlugin.register(with: registry.registrar(forPlugin: "FVPVideoPlayerPlugin"))
  VisionKitPlugin.register(with: registry.registrar(forPlugin: "VisionKitPlugin"))
}
