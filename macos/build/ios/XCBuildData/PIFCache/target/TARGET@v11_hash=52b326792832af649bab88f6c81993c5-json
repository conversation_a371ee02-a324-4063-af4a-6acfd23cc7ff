{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e627b81f2277a26392e82dc437b640b9", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98573cd727cd4f3e04ffb2d36a61797fb8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e983e3e2a440df885bf8225f319fc68b006", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984b6d27ca3f133a8809ce8858835ba048", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e986ef40b405151eb5d514ae470d05268ee", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e7509aec2a313e287c060651d9e8b1de", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98c75ad82d573af5a2e5f3bfa212e3b57f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a292699c7027eaa680bfe5da607e708f", "guid": "bfdfe7dc352907fc980b868725387e985f1ca42dbef8ffb5ad578bac0fabd89b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852b8cf46dbb173cb75a3b48bbfbb8858", "guid": "bfdfe7dc352907fc980b868725387e98d4cfa9b05e07090d241f15c02791c75e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835648ce799d62afd8c7500844a76b60e", "guid": "bfdfe7dc352907fc980b868725387e98db843f46bda636092beb60c9b755a3f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff058d7b390b6f2003ce67e8d91cd71", "guid": "bfdfe7dc352907fc980b868725387e981a3ad2dfa16deb077f1e83423fd5daa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98defb1e32e9bd8aba19d9a67f858e60b0", "guid": "bfdfe7dc352907fc980b868725387e98c015016c11ed5bb5a3b9e2eeb197af0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808a0a427f1edd3500994a26f3b1b4614", "guid": "bfdfe7dc352907fc980b868725387e98e8f17a084068b4cfef209259911200fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a490e75903fb2b0a8fb899a90d4287ed", "guid": "bfdfe7dc352907fc980b868725387e98a2b7cc6b9cad5715f8fb4ee289bf3421"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c76b719348323ba9f42f0d81d70b161", "guid": "bfdfe7dc352907fc980b868725387e98660448708758e72d480fc61915e74d74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980962a367e6152e96b70a125d815020af", "guid": "bfdfe7dc352907fc980b868725387e98573af3f9e72482d4092bab8309ef8f3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a11f800dc26d56426f6b37f8a283b0b", "guid": "bfdfe7dc352907fc980b868725387e98543ba488a6c06378dc299a13a93e67e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08b2c243127aba3d1fc2398f7ba08f3", "guid": "bfdfe7dc352907fc980b868725387e982de2311135c20e89a0697701a9ccf1be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989796f8ce5b602046976a913145a66f6c", "guid": "bfdfe7dc352907fc980b868725387e988cd465d8f55166a4a9a9b18b7ed5c61b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986968906746efa810a96cf74bfa70670d", "guid": "bfdfe7dc352907fc980b868725387e986ebe807d2107de9cc151af5a22049b15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98936c99140c6d493cc2084861c68133a2", "guid": "bfdfe7dc352907fc980b868725387e987fdc67d5a90915d7540245acf71dd092"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9017cb198b86bc9ed0cf016c9d298fc", "guid": "bfdfe7dc352907fc980b868725387e98294dc0bd9b97257490edf6cc08eeec28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135e59325df8adc673d9b81eb08333fb", "guid": "bfdfe7dc352907fc980b868725387e98c9676e1ec2e42001a23a20a6c36bdb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edc8787438970f71c64e1d428d742b0c", "guid": "bfdfe7dc352907fc980b868725387e98a028bd2996c53baf7a9f3a2f3583817b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878653648422e683002192a6134cd98ad", "guid": "bfdfe7dc352907fc980b868725387e9816143d96afe3547d35eaf60d8096c4ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c46c4af4d494babb82844bf0d68a4f4", "guid": "bfdfe7dc352907fc980b868725387e986c781f7873c80d1c10d249e2527ea93a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbca80ed18432ed6f1cbccf6775b30bb", "guid": "bfdfe7dc352907fc980b868725387e98e8397e66088344bafa64b59a9d5c6dc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1e46671a6db2c214e25ea65b3d2809b", "guid": "bfdfe7dc352907fc980b868725387e98ece37936b68349afdf41b94cdd672f21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7b40e8c8c2cda51e62b5c5e2d2129be", "guid": "bfdfe7dc352907fc980b868725387e980f2930a34edee370e0a9cf22c4ae1083"}], "guid": "bfdfe7dc352907fc980b868725387e98de530439b6fa6376394201f39a3c8dce", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}