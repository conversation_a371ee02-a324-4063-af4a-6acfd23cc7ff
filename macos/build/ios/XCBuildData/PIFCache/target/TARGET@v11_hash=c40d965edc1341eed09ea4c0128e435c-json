{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d017df6eb26a1783b874214268dea26", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f6005074a35dc49051fd2409b3659a0", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f191c54bfc8bd2078a4337a8bbbe4787", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9817d43bb302fcaaa5d12e1858e26a8e5d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f191c54bfc8bd2078a4337a8bbbe4787", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/SwiftyGif/SwiftyGif-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/SwiftyGif/SwiftyGif-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/SwiftyGif/SwiftyGif.modulemap", "PRODUCT_MODULE_NAME": "SwiftyGif", "PRODUCT_NAME": "SwiftyGif", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cda51cd537f27ce7b5fa96966174cf33", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98841e69c77a33c992a383444bcd690833", "guid": "bfdfe7dc352907fc980b868725387e981bdb82c787d71b20fc0ba49f9a0ba160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b9414b84d9ae46c6cfb7c8966e95c73", "guid": "bfdfe7dc352907fc980b868725387e9825bb593c45752fbb877c814f1dcbe6b0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e980de4be9964eaa7056b0fca68d7214d38", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e5ec22b9c4ec919ca16aaf1106fdfda7", "guid": "bfdfe7dc352907fc980b868725387e98d56b336cb5567fe03cbd57256e481a72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1f3f2ed47d0040183997f8bbf5e6ac1", "guid": "bfdfe7dc352907fc980b868725387e9891074872fdf9abc5510e745422c99e49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5362145f0a8c83082dc613a6cf5234", "guid": "bfdfe7dc352907fc980b868725387e9888d602f87f50455d459f8b0ace897400"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec31ea89f37bf925e7a4e918d80427c7", "guid": "bfdfe7dc352907fc980b868725387e98a3e2d8bde564deb33edc89f40fff7a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a09117dc7d3b1c63324941c308702cff", "guid": "bfdfe7dc352907fc980b868725387e986f0720c4b343c140894b2c8578b42b90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da799bba8441f8c7fb816c76aba9c6c5", "guid": "bfdfe7dc352907fc980b868725387e9888803fa95ce35d23e7ecfd75578d2a3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e813830c6fcdcb4cb8bc6548d3e992d1", "guid": "bfdfe7dc352907fc980b868725387e98cec1df05348c54b69b5796a9e9adaa3e"}], "guid": "bfdfe7dc352907fc980b868725387e98250dc74623fac5ea157189e8275556f2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988576e044c17abaca213b163abc086807", "guid": "bfdfe7dc352907fc980b868725387e983cdc47b7d980da6ae25dd8972d5b1b39"}], "guid": "bfdfe7dc352907fc980b868725387e9873e23e432a47cd07d4593c0c2ae72450", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c17c455ccc93b42a1ce71e1b85dcf1a", "targetReference": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c"}], "guid": "bfdfe7dc352907fc980b868725387e98cef78dedd189d5645274dc8bc31b7496", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98f5cd644fc2aeb8654450a2168f52697c", "name": "SwiftyGif-SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98290968646de0d07c6f6e2ed9e146ea78", "name": "SwiftyGif.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}