{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a03bcb7bb6864381e0c6db1443071c5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981e84b746a6eb462e0e11a10b060c7571", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981e84b746a6eb462e0e11a10b060c7571", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/dev/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d53a6a4f834d5ef30e7801824f0ef198", "guid": "bfdfe7dc352907fc980b868725387e98dd2b3863c5d15e93e7c5e88a896aefa1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98808c48b009f78fc87e14a28597efcf14", "guid": "bfdfe7dc352907fc980b868725387e987cb9e8b5abd65af173bb488380c96261", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862109c30fe49cb8a1c7f018db4d5911f", "guid": "bfdfe7dc352907fc980b868725387e982b1b9310b853c9d96a1d5b4a1e72b68d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984711d33c14ad123c9c74eef723fc1520", "guid": "bfdfe7dc352907fc980b868725387e98f6b72aa5e570dfa8ee6058a7df3eda77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247c6d5b6f93ffd5aabccbefdc5c997b", "guid": "bfdfe7dc352907fc980b868725387e9854d2145d07da2e08b0a1df7b08acf002", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd23d0c0d4bd85d47fc4000d3d476077", "guid": "bfdfe7dc352907fc980b868725387e984276f5741a37e5d035d42a235e6ad705", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cb17f72da21efbc1c80c1b51a8b43f0", "guid": "bfdfe7dc352907fc980b868725387e980642a548123fbfa1eb89a9fe2c311d35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4957641fa446e077c7bbdc6ddf8ec72", "guid": "bfdfe7dc352907fc980b868725387e98de912f230bb0c142eee21bdd7bbecff8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fee898d3fa5b9c23077592c5377e3f9", "guid": "bfdfe7dc352907fc980b868725387e9847057d6ee2db89872da5843bdb4dc996", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871b7d739173fbad2b04d1294115a2209", "guid": "bfdfe7dc352907fc980b868725387e98f5e683072cb2f2ccd7cc8dad0ee3dc53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245595f17583dcd82baebc4d3c873638", "guid": "bfdfe7dc352907fc980b868725387e989f6fdbdb3ec8a4c13de3be35ebd3a5b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c697f38b526b440d7f14a6f2a62e966", "guid": "bfdfe7dc352907fc980b868725387e9897df0186bed821e10eceab57ec51bf0b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896b9abdba2df46002d87275f67fe7902", "guid": "bfdfe7dc352907fc980b868725387e980e966315eec3f80916ca8ca3c5bee4ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dc859c191f81fcb5dc7334d66600552", "guid": "bfdfe7dc352907fc980b868725387e9885b0ff48f4e24c58e4238e6f8f3b38c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f678a8526cb1e450ed5996bedfa185ef", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981d6c39d2642b1eddecd702bc23194b7b", "guid": "bfdfe7dc352907fc980b868725387e982bd829b14a0ccbe16ad793a4119d2cd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98701d0c05431c33460257b7b824611530", "guid": "bfdfe7dc352907fc980b868725387e98af9660ddc2107a9cd410d6c3ed7be15f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f55bfca5414ff5c4e3e30699bd084db", "guid": "bfdfe7dc352907fc980b868725387e98b20853401f59963e0a4d3e87e87fda01"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878681234db5383d2d8d63b1728bfb5ec", "guid": "bfdfe7dc352907fc980b868725387e98de533744753a7ee373399d119e5e62fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e26af595cfc473c3be1f688a3f704f3", "guid": "bfdfe7dc352907fc980b868725387e98ce1d715dedbb66dfad0f4a1f232cde2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c05e9163a31477703442eecfbcc05d", "guid": "bfdfe7dc352907fc980b868725387e98b9c85fc42cd52bd6aa0aa90c356a052b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a36fd8c32d3fe2cbd7d84d6307d89d4", "guid": "bfdfe7dc352907fc980b868725387e98c93540ae74f80402c0ab2b6880c64bc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb5939d5c669a047f1724a9c2a1b316a", "guid": "bfdfe7dc352907fc980b868725387e9869d59e5f6a2852cb69ab4b97d4ec36ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980dcfbb19d2152654b7fbdf852937649f", "guid": "bfdfe7dc352907fc980b868725387e98c941ce29050e90d0d1202b7f5aefd176"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ca34dd4539b943087729875ec6d2b3f", "guid": "bfdfe7dc352907fc980b868725387e98c96a14345cd4a18708b9d6511a08430b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982eff61f59da2c607a9f710cd069eb9e1", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988576e044c17abaca213b163abc086807", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}