#ifndef whisper_bridge_h
#define whisper_bridge_h

#include <stdbool.h>
#include <stdint.h>

#if __cplusplus
extern "C" {
#endif

// This is a C-style wrapper for the whisper_init_from_file function.
// It allows us to call the C++ function from Dart FFI.
void* whisper_init_from_file_wrapper(const char* path_model);

// Define other C-style wrappers for all whisper functions used by the FFI.
// Ensure the function signatures match what the Dart FFI expects.

void whisper_free_wrapper(void* ctx);

struct whisper_full_params* whisper_full_default_params_wrapper(int strategy);

int whisper_full_wrapper(void* ctx, struct whisper_full_params* params, const float* samples, int n_samples);

int whisper_full_n_segments_wrapper(void* ctx);

const char* whisper_full_get_segment_text_wrapper(void* ctx, int i);

#if __cplusplus
}
#endif

#endif /* whisper_bridge_h */ 