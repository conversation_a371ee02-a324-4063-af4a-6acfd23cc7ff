//
// whisper_bridge_macos.cpp - macOS whisper bridge implementation
// Provides C-style wrappers for whisper.cpp integration
//

#include "whisper_bridge_macos.h"
#include <whisper.h>
#include <cstdlib>
#include <cstring>

// Initialize whisper context from model file
void* whisper_init_from_file_wrapper(const char* path_model) {
    struct whisper_context_params cparams = whisper_context_default_params();
    return whisper_init_from_file_with_params(path_model, cparams);
}

// Free whisper context
void whisper_free_wrapper(void* ctx) {
    if (ctx) {
        whisper_free(static_cast<struct whisper_context*>(ctx));
    }
}

// Get default parameters
struct whisper_full_params* whisper_full_default_params_wrapper(int strategy) {
    static struct whisper_full_params params;
    params = whisper_full_default_params(static_cast<whisper_sampling_strategy>(strategy));
    return &params;
}

// Run full transcription
int whisper_full_wrapper(void* ctx, struct whisper_full_params* params, const float* samples, int n_samples) {
    if (!ctx || !params || !samples) {
        return -1;
    }
    
    return whisper_full(
        static_cast<struct whisper_context*>(ctx),
        *params,
        samples,
        n_samples
    );
}

// Get number of segments
int whisper_full_n_segments_wrapper(void* ctx) {
    if (!ctx) {
        return 0;
    }
    
    return whisper_full_n_segments(static_cast<struct whisper_context*>(ctx));
}

// Get segment text
const char* whisper_full_get_segment_text_wrapper(void* ctx, int i) {
    if (!ctx) {
        return nullptr;
    }
    
    return whisper_full_get_segment_text(static_cast<struct whisper_context*>(ctx), i);
} 