//
// whisper_bridge_macos.h - macOS whisper bridge
// Provides C-style wrappers for whisper.cpp integration
//

#ifndef WHISPER_BRIDGE_MACOS_H
#define WHISPER_BRIDGE_MACOS_H

#ifdef __cplusplus
extern "C" {
#endif

// Initialize whisper context from model file
void* whisper_init_from_file_wrapper(const char* path_model);

// Free whisper context
void whisper_free_wrapper(void* ctx);

// Get default parameters
struct whisper_full_params* whisper_full_default_params_wrapper(int strategy);

// Run full transcription
int whisper_full_wrapper(void* ctx, struct whisper_full_params* params, const float* samples, int n_samples);

// Get number of segments
int whisper_full_n_segments_wrapper(void* ctx);

// Get segment text
const char* whisper_full_get_segment_text_wrapper(void* ctx, int i);

#ifdef __cplusplus
}
#endif

#endif /* WHISPER_BRIDGE_MACOS_H */ 