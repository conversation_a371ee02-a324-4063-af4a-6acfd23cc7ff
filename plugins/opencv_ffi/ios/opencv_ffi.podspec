#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint opencv_ffi.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'opencv_ffi'
  s.version          = '0.0.1'
  s.summary          = 'A new Flutter FFI plugin project.'
  s.description      = <<-DESC
A new Flutter FFI plugin project.
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }

  # This will ensure the source files in Classes/ are included in the native
  # builds of apps using this FFI plugin. Podspec does not support relative
  # paths, so Classes contains a forwarder C file that relatively imports
  # `../src/*` so that the C sources can be shared among all target platforms.
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.public_header_files = 'Classes/**/*.h'
  s.dependency 'Flutter'
  s.platform = :ios, '15.0'

  # Enable C++ compilation and link to existing OpenCV framework
  s.pod_target_xcconfig = { 
    'DEFINES_MODULE' => 'YES', 
    'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386',
    'CLANG_CXX_LANGUAGE_STANDARD' => 'c++17',
    'CLANG_CXX_LIBRARY' => 'libc++',
    'FRAMEWORK_SEARCH_PATHS' => '$(inherited) $(PODS_TARGET_SRCROOT)/Frameworks',
    'HEADER_SEARCH_PATHS' => '$(inherited) $(PODS_TARGET_SRCROOT)/Frameworks/opencv2.framework/Headers',
    'GCC_PREPROCESSOR_DEFINITIONS' => 'DART_SHARED_LIB=1'
  }
  
  # Bundle existing OpenCV framework with our wrapper
  s.vendored_frameworks = 'Frameworks/opencv2.framework'
  s.frameworks = 'AVFoundation', 'CoreVideo', 'CoreMedia', 'CoreImage', 'QuartzCore', 'Accelerate'
  s.libraries = 'c++', 'z'
  
  s.swift_version = '5.0'
end
