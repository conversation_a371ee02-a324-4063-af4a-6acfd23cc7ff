import Flutter
import UIKit
import AVFoundation
import Vision

@available(iOS 15.0, *)
public class VisionKitPlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "visionkit", binaryMessenger: registrar.messenger())
    let instance = VisionKitPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    switch call.method {
    case "getPlatformVersion":
      result("iOS " + UIDevice.current.systemVersion)
    case "detectTextInVideo":
        handleDetectText(call: call, result: result)
    case "detectObjectsInVideo":
        handleDetectObjects(call: call, result: result)
    case "analyzeVideoComprehensively":
        handleComprehensiveAnalysis(call: call, result: result)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

  private func handleDetectText(call: FlutterMethod<PERSON>all, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleDetectText() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let textDetections = try self.performTextDetectionSync(videoPath: videoPath)
              print("[VisionKitPlugin.swift] Detection finished, found \(textDetections.count) items.")
              DispatchQueue.main.async {
                  result(textDetections)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during detection: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "DETECTION_FAILED", message: "Failed to perform text detection: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performTextDetectionSync(videoPath: String) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performTextDetectionSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)
      
      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }
      
      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero
      
      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze more frames and spread them throughout the video
      let maxFrames = min(15, max(5, Int(durationInSeconds / 4)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames.")
      
      var allDetections: [[String: Any]] = []
      
      if maxFrames == 0 {
          return allDetections
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600) // Add small offset
              
              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let detections = try self.recognizeTextSync(in: cgImage, at: time)
                  
                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Found \(detections.count) texts")
                  
                  if !detections.isEmpty {
                      print("[VisionKitPlugin.swift] Detections: \(detections.map { $0["text"] ?? "unknown" })")
                      allDetections.append(contentsOf: detections)
                  }
              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }
      
      print("[VisionKitPlugin.swift] Total detections found: \(allDetections.count)")
      return allDetections
  }

  private func recognizeTextSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let request = VNRecognizeTextRequest()
      request.recognitionLevel = .accurate // Use accurate recognition for better detection
      request.usesLanguageCorrection = true // Enable language correction for better results
      
      // Support multiple languages
      request.recognitionLanguages = ["en-US", "en-GB", "es-ES", "fr-FR", "de-DE", "it-IT", "pt-BR", "zh-CN", "ja-JP"]
      
      // Enable automatic language detection (iOS 16.0+)
      if #available(iOS 16.0, *) {
          request.automaticallyDetectsLanguage = true
      }

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([request])

      guard let observations = request.results else {
          return []
      }

      print("[VisionKitPlugin.swift] Frame analysis: found \(observations.count) text observations")
      
      let validDetections = observations.compactMap { observation -> [String: Any]? in
          guard let topCandidate = observation.topCandidates(1).first else { 
              return nil 
          }
          
          print("[VisionKitPlugin.swift] Text candidate: '\(topCandidate.string)' confidence: \(topCandidate.confidence)")
          
          // Lower confidence threshold to catch more text
          guard topCandidate.confidence > 0.2 else {
              print("[VisionKitPlugin.swift] Rejected low confidence: \(topCandidate.confidence)")
              return nil
          }
          
          // Filter out very short or nonsensical text
          let trimmedText = topCandidate.string.trimmingCharacters(in: .whitespacesAndNewlines)
          guard trimmedText.count >= 2 else {
              print("[VisionKitPlugin.swift] Rejected short text: '\(trimmedText)'")
              return nil
          }
          
          return [
              "text": trimmedText,
              "confidence": topCandidate.confidence,
              "timestamp": time.seconds,
              "box": [
                  "x": observation.boundingBox.origin.x,
                  "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                  "width": observation.boundingBox.size.width,
                  "height": observation.boundingBox.size.height
              ]
          ]
      }
      
      print("[VisionKitPlugin.swift] Valid detections after filtering: \(validDetections.count)")
      return validDetections
  }

  private func handleDetectObjects(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleDetectObjects() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let objectDetections = try self.performObjectDetectionSync(videoPath: videoPath)
              print("[VisionKitPlugin.swift] Object detection finished, found \(objectDetections.count) items.")
              DispatchQueue.main.async {
                  result(objectDetections)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during object detection: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "DETECTION_FAILED", message: "Failed to perform object detection: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performObjectDetectionSync(videoPath: String) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performObjectDetectionSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)

      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }

      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero

      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze frames for object detection
      let maxFrames = min(20, max(8, Int(durationInSeconds / 3)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames for objects.")

      var allDetections: [[String: Any]] = []

      if maxFrames == 0 {
          return allDetections
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600)

              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let detections = try self.recognizeObjectsSync(in: cgImage, at: time)

                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Found \(detections.count) objects")

                  if !detections.isEmpty {
                      print("[VisionKitPlugin.swift] Object detections: \(detections.map { $0["label"] ?? "unknown" })")
                      allDetections.append(contentsOf: detections)
                  }
              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }

      print("[VisionKitPlugin.swift] Total object detections found: \(allDetections.count)")
      return allDetections
  }

  private func recognizeObjectsSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let request = VNRecognizeAnimalsRequest()
      let objectRequest = VNDetectHumanRectanglesRequest()

      // For iOS 17.0+ we can use more advanced object detection
      var requests: [VNRequest] = [request, objectRequest]

      if #available(iOS 17.0, *) {
          // Add more sophisticated object detection for newer iOS versions
          let advancedRequest = VNDetectHumanBodyPoseRequest()
          requests.append(advancedRequest)
      }

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform(requests)

      var allDetections: [[String: Any]] = []

      // Process animal detections
      if let animalObservations = request.results {
          print("[VisionKitPlugin.swift] Found \(animalObservations.count) animal observations")

          for observation in animalObservations {
              guard observation.confidence > 0.3 else { continue }

              let animalLabels = observation.labels.compactMap { label -> String? in
                  guard label.confidence > 0.3 else { return nil }
                  return label.identifier
              }

              if !animalLabels.isEmpty {
                  allDetections.append([
                      "label": "Animal: \(animalLabels.joined(separator: ", "))",
                      "confidence": observation.confidence,
                      "timestamp": time.seconds,
                      "box": [
                          "x": observation.boundingBox.origin.x,
                          "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                          "width": observation.boundingBox.size.width,
                          "height": observation.boundingBox.size.height
                      ],
                      "metadata": [
                          "type": "animal",
                          "labels": animalLabels,
                          "count": animalLabels.count
                      ]
                  ])
              }
          }
      }

      // Process human detections
      if let humanObservations = objectRequest.results {
          print("[VisionKitPlugin.swift] Found \(humanObservations.count) human observations")

          for observation in humanObservations {
              guard observation.confidence > 0.4 else { continue }

              allDetections.append([
                  "label": "Person",
                  "confidence": observation.confidence,
                  "timestamp": time.seconds,
                  "box": [
                      "x": observation.boundingBox.origin.x,
                      "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                      "width": observation.boundingBox.size.width,
                      "height": observation.boundingBox.size.height
                  ],
                  "metadata": [
                      "type": "person",
                      "upperBodyOnly": observation.upperBodyOnly
                  ]
              ])
          }
      }

      print("[VisionKitPlugin.swift] Valid object detections after filtering: \(allDetections.count)")
      return allDetections
  }

  private func detectFacesSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      // Create multiple face detection requests
      let faceRectanglesRequest = VNDetectFaceRectanglesRequest()
      let faceLandmarksRequest = VNDetectFaceLandmarksRequest()
      let faceQualityRequest = VNDetectFaceCaptureQualityRequest()

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([faceRectanglesRequest, faceLandmarksRequest, faceQualityRequest])

      var allFaceDetections: [[String: Any]] = []

      // Process face rectangles (basic face detection)
      if let faceObservations = faceRectanglesRequest.results {
          print("[VisionKitPlugin.swift] Found \(faceObservations.count) face rectangles")

          for observation in faceObservations {
              guard observation.confidence > 0.5 else { continue }

              // Find corresponding landmarks and quality for this face
              let landmarks = self.findFaceLandmarks(for: observation, in: faceLandmarksRequest.results)
              let quality = self.calculateFaceQuality(for: observation, in: faceQualityRequest.results)
              let analysis = self.analyzeFaceCharacteristics(observation: observation, landmarks: landmarks)

              let faceDetection: [String: Any] = [
                  "confidence": observation.confidence,
                  "timestamp": time.seconds,
                  "box": [
                      "x": observation.boundingBox.origin.x,
                      "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                      "width": observation.boundingBox.size.width,
                      "height": observation.boundingBox.size.height
                  ],
                  "landmarks": landmarks,
                  "quality": quality,
                  "analysis": analysis
              ]

              allFaceDetections.append(faceDetection)
          }
      }

      return allFaceDetections
  }

  private func detectBodyPoseSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let bodyPoseRequest = VNDetectHumanBodyPoseRequest()

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([bodyPoseRequest])

      var allBodyPoseDetections: [[String: Any]] = []

      if let poseObservations = bodyPoseRequest.results {
          print("[VisionKitPlugin.swift] Found \(poseObservations.count) body pose observations")

          for observation in poseObservations {
              guard observation.confidence > 0.3 else { continue }

              // Extract keypoints and analyze pose
              let keypoints = self.extractBodyKeypoints(from: observation)
              let analysis = self.analyzeBodyPose(observation: observation, keypoints: keypoints)

              let bodyPoseDetection: [String: Any] = [
                  "confidence": observation.confidence,
                  "timestamp": time.seconds,
                  "keypoints": keypoints,
                  "analysis": analysis,
                  "metadata": [
                      "keypointCount": keypoints.count,
                      "detectionType": "humanBodyPose"
                  ]
              ]

              allBodyPoseDetections.append(bodyPoseDetection)
          }
      }

      print("[VisionKitPlugin.swift] Valid body pose detections after filtering: \(allBodyPoseDetections.count)")
      return allBodyPoseDetections
  }

  private func detectHandPoseSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let handPoseRequest = VNDetectHumanHandPoseRequest()
      handPoseRequest.maximumHandCount = 4 // Detect up to 4 hands

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([handPoseRequest])

      var allHandPoseDetections: [[String: Any]] = []

      if let handObservations = handPoseRequest.results {
          print("[VisionKitPlugin.swift] Found \(handObservations.count) hand pose observations")

          for observation in handObservations {
              guard observation.confidence > 0.5 else { continue }

              // Extract hand keypoints and analyze gesture
              let keypoints = self.extractHandKeypoints(from: observation)
              let gesture = self.analyzeHandGesture(observation: observation, keypoints: keypoints)
              let handType = self.determineHandType(observation: observation)

              let handPoseDetection: [String: Any] = [
                  "confidence": observation.confidence,
                  "timestamp": time.seconds,
                  "handType": handType,
                  "keypoints": keypoints,
                  "gesture": gesture,
                  "metadata": [
                      "keypointCount": keypoints.count,
                      "detectionType": "humanHandPose"
                  ]
              ]

              allHandPoseDetections.append(handPoseDetection)
          }
      }

      print("[VisionKitPlugin.swift] Valid hand pose detections after filtering: \(allHandPoseDetections.count)")
      return allHandPoseDetections
  }

  private func handleComprehensiveAnalysis(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleComprehensiveAnalysis() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }

      let options = args["options"] as? [String: Any] ?? [:]
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath), options: \(options)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let analysisResults = try self.performComprehensiveAnalysisSync(videoPath: videoPath, options: options)
              print("[VisionKitPlugin.swift] Comprehensive analysis finished, found \(analysisResults.count) results.")
              DispatchQueue.main.async {
                  result(analysisResults)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during comprehensive analysis: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "ANALYSIS_FAILED", message: "Failed to perform comprehensive analysis: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performComprehensiveAnalysisSync(videoPath: String, options: [String: Any]) throws -> [[String: Any]] {
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)

      // Check if asset is valid
      guard asset.isPlayable else {
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }

      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero

      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)

      // Analyze more frames for comprehensive analysis
      let maxFrames = min(25, max(10, Int(durationInSeconds / 2)))

      var allAnalysisResults: [[String: Any]] = []

      if maxFrames == 0 {
          return allAnalysisResults
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600)

              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let analysisResult = try self.performFrameAnalysis(image: cgImage, at: time, options: options)
                  allAnalysisResults.append(analysisResult)
              } catch {
                  // Continue with other frames even if one fails
              }
          }
      }

      print("[VisionKitPlugin.swift] Total comprehensive analysis results: \(allAnalysisResults.count)")

      // Log clean JSON data being returned to Flutter
      print("=== SWIFT VISIONKIT JSON ===")

      // Save complete analysis to JSON file for debug purposes
      let completeAnalysis = [
          "videoPath": videoPath,
          "analysisTimestamp": ISO8601DateFormatter().string(from: Date()),
          "totalFrames": allAnalysisResults.count,
          "results": allAnalysisResults
      ]

      if let jsonData = try? JSONSerialization.data(withJSONObject: completeAnalysis, options: .prettyPrinted) {
          let jsonString = String(data: jsonData, encoding: .utf8) ?? ""
          print(jsonString)

          // Save to /tmp directory for easy debug access
          let tmpDirectory = URL(fileURLWithPath: NSTemporaryDirectory())
          let dateFormatter = DateFormatter()
          dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
          let timestamp = dateFormatter.string(from: Date())
          let fileName = "visionkit_analysis_\(timestamp).json"
          let fileURL = tmpDirectory.appendingPathComponent(fileName)

          do {
              try jsonData.write(to: fileURL)
              print("✅ JSON saved to: \(fileURL.path)")
          } catch {
              print("❌ Failed to save JSON: \(error)")
          }
      }

      print("=== END SWIFT JSON ===")

      return allAnalysisResults
  }

  private func performFrameAnalysis(image: CGImage, at time: CMTime, options: [String: Any]) throws -> [String: Any] {
      // Perform all types of analysis on this frame
      let textDetections = try self.recognizeTextSync(in: image, at: time)
      let objectDetections = try self.recognizeObjectsSync(in: image, at: time)
      let faceDetections = try self.detectFacesSync(in: image, at: time)
      let bodyPoseDetections = try self.detectBodyPoseSync(in: image, at: time)
      let handPoseDetections = try self.detectHandPoseSync(in: image, at: time)
      let sceneAnalysis = try self.analyzeScene(in: image, at: time)

      // Calculate additional metrics for future fusion
      let visualMetrics = self.calculateVisualMetrics(image: image)
      let contentDensity = self.calculateContentDensity(
          textCount: textDetections.count,
          objectCount: objectDetections.count,
          faceCount: faceDetections.count,
          poseCount: bodyPoseDetections.count,
          handCount: handPoseDetections.count,
          imageSize: CGSize(width: image.width, height: image.height)
      )
      let temporalContext = self.calculateTemporalContext(timestamp: time.seconds)

      // Calculate interest score based on detections
      let interestScore = self.calculateInterestScore(
          textDetections: textDetections,
          objectDetections: objectDetections,
          faceDetections: faceDetections,
          bodyPoseDetections: bodyPoseDetections,
          handPoseDetections: handPoseDetections,
          sceneAnalysis: sceneAnalysis,
          options: options
      )

      // Enhanced JSON with rich metadata for future needs
      return [
          "timestamp": time.seconds,
          "textDetections": textDetections,
          "objectDetections": objectDetections,
          "faceDetections": faceDetections,
          "bodyPoseDetections": bodyPoseDetections,
          "handPoseDetections": handPoseDetections,
          "sceneAnalysis": sceneAnalysis,
          "interestScore": interestScore,

          // Enhanced metadata for multi-modal fusion
          "metadata": [
              // Basic frame info
              "frameWidth": image.width,
              "frameHeight": image.height,
              "analysisTime": Date().timeIntervalSince1970,

              // Visual quality metrics
              "visualMetrics": visualMetrics,

              // Content density for highlight scoring
              "contentDensity": contentDensity,

              // Temporal context for sequence analysis
              "temporalContext": temporalContext,

              // Counts for quick filtering
              "counts": self.calculateCounts(
                  objectDetections: objectDetections,
                  textDetections: textDetections,
                  faceDetections: faceDetections
              ),

              // Fusion readiness flags
              "fusionReady": self.calculateFusionFlags(
                  objectDetections: objectDetections,
                  textDetections: textDetections,
                  faceDetections: faceDetections,
                  interestScore: interestScore,
                  visualMetrics: visualMetrics
              )
          ]
      ]
  }

  private func analyzeScene(in image: CGImage, at time: CMTime) throws -> [String: Any] {
      let request = VNClassifyImageRequest()

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([request])

      guard let observations = request.results else {
          return [
              "sceneType": "unknown",
              "confidence": 0.0,
              "timestamp": time.seconds,
              "attributes": [:]
          ]
      }

      // Get the top scene classification
      let topObservation = observations.first
      let sceneType = topObservation?.identifier ?? "unknown"
      let confidence = topObservation?.confidence ?? 0.0

      // Analyze lighting and other attributes
      let attributes = self.analyzeImageAttributes(image: image)

      print("[VisionKitPlugin.swift] Scene analysis - Type: \(sceneType), Confidence: \(confidence)")

      return [
          "sceneType": sceneType,
          "confidence": confidence,
          "timestamp": time.seconds,
          "attributes": attributes
      ]
  }

  private func analyzeImageAttributes(image: CGImage) -> [String: Any] {
      // Basic image quality analysis
      let width = image.width
      let height = image.height
      let aspectRatio = Double(width) / Double(height)

      // Estimate brightness (simplified)
      let brightness = self.estimateBrightness(image: image)

      return [
          "width": width,
          "height": height,
          "aspectRatio": aspectRatio,
          "brightness": brightness,
          "quality": brightness > 0.3 ? "good" : "poor"
      ]
  }

  private func estimateBrightness(image: CGImage) -> Double {
      // Real brightness estimation using Core Image
      let ciImage = CIImage(cgImage: image)
      let context = CIContext()

      // Use Core Image to calculate average brightness
      let filter = CIFilter(name: "CIAreaAverage")!
      filter.setValue(ciImage, forKey: kCIInputImageKey)
      filter.setValue(CIVector(cgRect: ciImage.extent), forKey: kCIInputExtentKey)

      guard let outputImage = filter.outputImage else {
          return 0.5 // Fallback if analysis fails
      }

      var bitmap = [UInt8](repeating: 0, count: 4)
      context.render(outputImage, toBitmap: &bitmap, rowBytes: 4, bounds: CGRect(x: 0, y: 0, width: 1, height: 1), format: .RGBA8, colorSpace: nil)

      // Calculate brightness from RGB values
      let brightness = (Double(bitmap[0]) + Double(bitmap[1]) + Double(bitmap[2])) / (3.0 * 255.0)
      return brightness
  }

  private func calculateInterestScore(
      textDetections: [[String: Any]],
      objectDetections: [[String: Any]],
      faceDetections: [[String: Any]],
      bodyPoseDetections: [[String: Any]],
      handPoseDetections: [[String: Any]],
      sceneAnalysis: [String: Any],
      options: [String: Any]
  ) -> Double {
      var score: Double = 0.0

      // Base score from object detections
      let objectScore = self.calculateObjectScore(objectDetections: objectDetections)
      score += objectScore * 0.2

      // Score from face detections (very important for social moments)
      let faceScore = self.calculateFaceScore(faceDetections: faceDetections)
      score += faceScore * 0.3

      // Score from body pose detections (activities and gestures)
      let poseScore = self.calculatePoseScore(bodyPoseDetections: bodyPoseDetections)
      score += poseScore * 0.25

      // Score from hand pose detections (gestures and interactions)
      let handScore = self.calculateHandScore(handPoseDetections: handPoseDetections)
      score += handScore * 0.1

      // Score from text detections (important information)
      let textScore = self.calculateTextScore(textDetections: textDetections)
      score += textScore * 0.1

      // Scene quality and type score
      let sceneScore = self.calculateSceneScore(sceneAnalysis: sceneAnalysis)
      score += sceneScore * 0.05

      // Normalize to 0-1 range
      let finalScore = min(1.0, max(0.0, score))
      return finalScore
  }

  private func calculateObjectScore(objectDetections: [[String: Any]]) -> Double {
      guard !objectDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0
      var personCount = 0
      var animalCount = 0

      for detection in objectDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let label = detection["label"] as? String ?? ""

          if label.contains("Person") {
              personCount += 1
              score += confidence * 0.8 // People are very interesting
          } else if label.contains("Animal") {
              animalCount += 1
              score += confidence * 0.6 // Animals are interesting
          } else {
              score += confidence * 0.3 // Other objects
          }
      }

      // Bonus for multiple people (social interaction)
      if personCount > 1 {
          score += 0.3
      }

      // Bonus for animals
      if animalCount > 0 {
          score += 0.2
      }

      return min(1.0, score)
  }

  private func calculateTextScore(textDetections: [[String: Any]]) -> Double {
      guard !textDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0

      for detection in textDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let text = detection["text"] as? String ?? ""

          // Higher score for longer, more confident text
          let lengthBonus = min(0.5, Double(text.count) / 20.0)
          score += (confidence + lengthBonus) * 0.5
      }

      return min(1.0, score / Double(textDetections.count))
  }

  private func calculateFaceScore(faceDetections: [[String: Any]]) -> Double {
      guard !faceDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0
      var highQualityFaces = 0
      var wellFramedFaces = 0
      var lookingAtCameraFaces = 0

      for detection in faceDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let quality = detection["quality"] as? [String: Any] ?? [:]
          let analysis = detection["analysis"] as? [String: Any] ?? [:]

          // Base score from confidence
          score += confidence * 0.5

          // Quality bonus
          let qualityScore = quality["score"] as? Double ?? 0.0
          if qualityScore > 0.7 {
              score += 0.3
              highQualityFaces += 1
          } else if qualityScore > 0.5 {
              score += 0.15
          }

          // Well-framed bonus
          if analysis["isWellFramed"] as? Bool == true {
              score += 0.2
              wellFramedFaces += 1
          }

          // Looking at camera bonus (great for photos)
          if analysis["isLookingAtCamera"] as? Bool == true {
              score += 0.25
              lookingAtCameraFaces += 1
          }

          // Large face bonus (close-up shots)
          if analysis["isLargeFace"] as? Bool == true {
              score += 0.15
          }
      }

      // Group photo bonus (multiple faces)
      if faceDetections.count > 1 {
          score += 0.4 // Social moments are very interesting
      }

      // Multiple high-quality faces bonus
      if highQualityFaces > 1 {
          score += 0.3
      }

      // All faces looking at camera bonus (perfect group photo)
      if lookingAtCameraFaces == faceDetections.count && faceDetections.count > 1 {
          score += 0.5
      }

      return min(1.0, score)
  }

  private func calculatePoseScore(bodyPoseDetections: [[String: Any]]) -> Double {
      guard !bodyPoseDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0
      var highEnergyPoses = 0
      var celebrationPoses = 0

      for detection in bodyPoseDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let analysis = detection["analysis"] as? [String: Any] ?? [:]

          // Base score from confidence
          score += confidence * 0.3

          // Activity bonuses
          let activity = analysis["activity"] as? String ?? ""
          switch activity {
          case "highActivity":
              score += 0.5
              highEnergyPoses += 1
          case "mediumActivity":
              score += 0.3
          case "lowActivity":
              score += 0.1
          default:
              break
          }

          // Gesture bonuses
          let gesture = analysis["gesture"] as? String ?? ""
          if gesture == "celebration" {
              score += 0.4
              celebrationPoses += 1
          } else if gesture == "pointing" {
              score += 0.2
          }

          // Energy level bonus
          let energyLevel = analysis["energyLevel"] as? Double ?? 0.0
          score += energyLevel * 0.3

          // Action pose bonus
          if analysis["isActionPose"] as? Bool == true {
              score += 0.2
          }
      }

      // Multiple high-energy poses bonus (group activities)
      if highEnergyPoses > 1 {
          score += 0.3
      }

      // Celebration bonus
      if celebrationPoses > 0 {
          score += 0.4
      }

      return min(1.0, score)
  }

  private func calculateHandScore(handPoseDetections: [[String: Any]]) -> Double {
      guard !handPoseDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0
      var gestureCount = 0

      for detection in handPoseDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let gesture = detection["gesture"] as? [String: Any] ?? [:]

          // Base score from confidence
          score += confidence * 0.4

          // Gesture bonuses
          let gestureType = gesture["type"] as? String ?? ""
          switch gestureType {
          case "thumbsUp":
              score += 0.5
              gestureCount += 1
          case "pointing":
              score += 0.3
              gestureCount += 1
          case "waving":
              score += 0.4
              gestureCount += 1
          case "open":
              score += 0.2
          default:
              break
          }

          // Gesture confidence bonus
          let gestureConfidence = gesture["confidence"] as? Double ?? 0.0
          score += gestureConfidence * 0.2
      }

      // Multiple gestures bonus (interactive moments)
      if gestureCount > 1 {
          score += 0.3
      }

      return min(1.0, score)
  }

  private func calculateSceneScore(sceneAnalysis: [String: Any]) -> Double {
      let confidence = sceneAnalysis["confidence"] as? Double ?? 0.0
      let sceneType = sceneAnalysis["sceneType"] as? String ?? ""
      let attributes = sceneAnalysis["attributes"] as? [String: Any] ?? [:]

      var score = confidence

      // Bonus for high-quality scenes
      if let quality = attributes["quality"] as? String, quality == "good" {
          score += 0.2
      }

      // Bonus for interesting scene types
      let interestingScenes = ["outdoor", "sports", "party", "performance", "celebration"]
      if interestingScenes.contains(where: { sceneType.lowercased().contains($0) }) {
          score += 0.3
      }

      return min(1.0, score)
  }

  private func calculateDiversityScore(hasText: Bool, hasObjects: Bool, hasScene: Bool) -> Double {
      let components = [hasText, hasObjects, hasScene].filter { $0 }.count
      return Double(components) / 3.0
  }

  // MARK: - Enhanced Metrics for Future Fusion

  private func calculateVisualMetrics(image: CGImage) -> [String: Any] {
      let brightness = self.estimateBrightness(image: image)
      let aspectRatio = Double(image.width) / Double(image.height)

      // Calculate sharpness estimate (simplified)
      let sharpness = self.estimateSharpness(image: image)

      // Overall quality score
      let overallQuality = (brightness * 0.4 + sharpness * 0.6)

      return [
          "brightness": brightness,
          "sharpness": sharpness,
          "aspectRatio": aspectRatio,
          "overallQuality": overallQuality,
          "resolution": [
              "width": image.width,
              "height": image.height,
              "megapixels": Double(image.width * image.height) / 1_000_000.0
          ]
      ]
  }

  private func calculateContentDensity(textCount: Int, objectCount: Int, faceCount: Int, poseCount: Int, handCount: Int, imageSize: CGSize) -> [String: Any] {
      let totalPixels = imageSize.width * imageSize.height
      let contentItems = Double(textCount + objectCount + faceCount + poseCount + handCount)
      let density = contentItems / (totalPixels / 1_000_000.0) // Items per megapixel

      return [
          "itemsPerMegapixel": density,
          "totalContentItems": Int(contentItems),
          "densityCategory": density > 5.0 ? "high" : (density > 2.0 ? "medium" : "low"),
          "hasRichContent": contentItems >= 3,
          "faceToObjectRatio": objectCount > 0 ? Double(faceCount) / Double(objectCount) : 0.0,
          "poseToFaceRatio": faceCount > 0 ? Double(poseCount) / Double(faceCount) : 0.0,
          "hasHumanActivity": poseCount > 0 || handCount > 0
      ]
  }

  private func calculateTemporalContext(timestamp: Double) -> [String: Any] {
      return [
          "timestamp": timestamp,
          "timeCategory": self.categorizeTimestamp(timestamp),
          "isEarlyInVideo": timestamp < 10.0,
          "isLateInVideo": false, // Would need video duration to calculate
          "roundedSecond": Int(timestamp.rounded()),
          "fractionalSecond": timestamp.truncatingRemainder(dividingBy: 1.0)
      ]
  }

  private func categorizeTimestamp(_ timestamp: Double) -> String {
      if timestamp < 5.0 { return "opening" }
      else if timestamp < 30.0 { return "early" }
      else if timestamp < 120.0 { return "middle" }
      else { return "late" }
  }

  private func estimateSharpness(image: CGImage) -> Double {
      // Simplified sharpness estimation
      // In a real implementation, you'd analyze edge detection or frequency domain
      let width = image.width
      let height = image.height

      // Assume higher resolution = potentially sharper (very simplified)
      let resolutionFactor = min(1.0, Double(width * height) / (1920.0 * 1080.0))

      // Return a reasonable estimate between 0.3 and 0.9
      return 0.3 + (resolutionFactor * 0.6)
  }

  // MARK: - Face Analysis Helper Methods

  private func findFaceLandmarks(for faceObservation: VNFaceObservation, in landmarkResults: [VNFaceObservation]?) -> [String: Any] {
      guard let landmarkResults = landmarkResults else { return [:] }

      // Find the landmark observation that matches this face (by proximity)
      for landmarkObservation in landmarkResults {
          let distance = self.calculateBoundingBoxDistance(faceObservation.boundingBox, landmarkObservation.boundingBox)
          if distance < 0.1 { // Close enough to be the same face
              return self.extractLandmarkData(from: landmarkObservation)
          }
      }

      return [:]
  }

  private func calculateFaceQuality(for faceObservation: VNFaceObservation, in qualityResults: [VNFaceObservation]?) -> [String: Any] {
      guard let qualityResults = qualityResults else {
          return ["score": 0.5, "category": "unknown"]
      }

      // Find the quality observation that matches this face
      for qualityObservation in qualityResults {
          let distance = self.calculateBoundingBoxDistance(faceObservation.boundingBox, qualityObservation.boundingBox)
          if distance < 0.1 {
              let score = qualityObservation.faceCaptureQuality ?? 0.5
              return [
                  "score": score,
                  "category": score > 0.7 ? "high" : (score > 0.4 ? "medium" : "low"),
                  "isPhotoWorthy": score > 0.6
              ]
          }
      }

      return ["score": 0.5, "category": "medium"]
  }

  private func analyzeFaceCharacteristics(observation: VNFaceObservation, landmarks: [String: Any]) -> [String: Any] {
      let faceSize = observation.boundingBox.width * observation.boundingBox.height
      let isLargeFace = faceSize > 0.05 // Face takes up more than 5% of image

      // Estimate if person is looking at camera (simplified)
      let isLookingAtCamera = self.estimateGazeDirection(landmarks: landmarks)

      return [
          "faceSize": faceSize,
          "isLargeFace": isLargeFace,
          "isLookingAtCamera": isLookingAtCamera,
          "facePosition": [
              "x": observation.boundingBox.midX,
              "y": observation.boundingBox.midY
          ],
          "isWellFramed": self.isFaceWellFramed(observation.boundingBox),
          "estimatedAge": "unknown", // Would need additional ML model
          "estimatedEmotion": "neutral" // Would need additional ML model
      ]
  }

  private func extractLandmarkData(from observation: VNFaceObservation) -> [String: Any] {
      var landmarks: [String: Any] = [:]

      if let allPoints = observation.landmarks?.allPoints {
          landmarks["allPoints"] = allPoints.normalizedPoints.map { ["x": $0.x, "y": $0.y] }
      }

      if let leftEye = observation.landmarks?.leftEye {
          landmarks["leftEye"] = leftEye.normalizedPoints.map { ["x": $0.x, "y": $0.y] }
      }

      if let rightEye = observation.landmarks?.rightEye {
          landmarks["rightEye"] = rightEye.normalizedPoints.map { ["x": $0.x, "y": $0.y] }
      }

      if let nose = observation.landmarks?.nose {
          landmarks["nose"] = nose.normalizedPoints.map { ["x": $0.x, "y": $0.y] }
      }

      if let outerLips = observation.landmarks?.outerLips {
          landmarks["mouth"] = outerLips.normalizedPoints.map { ["x": $0.x, "y": $0.y] }
      }

      return landmarks
  }

  private func calculateBoundingBoxDistance(_ box1: CGRect, _ box2: CGRect) -> Double {
      let center1 = CGPoint(x: box1.midX, y: box1.midY)
      let center2 = CGPoint(x: box2.midX, y: box2.midY)
      let dx = center1.x - center2.x
      let dy = center1.y - center2.y
      return sqrt(Double(dx * dx + dy * dy))
  }

  private func estimateGazeDirection(landmarks: [String: Any]) -> Bool {
      // Simplified gaze estimation - would need more sophisticated analysis
      // For now, assume looking at camera if we have eye landmarks
      return landmarks["leftEye"] != nil && landmarks["rightEye"] != nil
  }

  private func isFaceWellFramed(_ boundingBox: CGRect) -> Bool {
      // Check if face is well-positioned in frame
      let centerX = boundingBox.midX
      let centerY = boundingBox.midY

      // Face should be roughly in center third of image
      let isHorizontallyCentered = centerX > 0.33 && centerX < 0.67
      let isVerticallyCentered = centerY > 0.25 && centerY < 0.75

      return isHorizontallyCentered && isVerticallyCentered
  }

  // MARK: - Body Pose Analysis Methods

  private func extractBodyKeypoints(from observation: VNHumanBodyPoseObservation) -> [String: Any] {
      var keypoints: [String: Any] = [:]

      // Define the body joints we want to track
      let jointNames: [VNHumanBodyPoseObservation.JointName] = [
          .nose, .leftEye, .rightEye, .leftEar, .rightEar,
          .leftShoulder, .rightShoulder, .leftElbow, .rightElbow,
          .leftWrist, .rightWrist, .leftHip, .rightHip,
          .leftKnee, .rightKnee, .leftAnkle, .rightAnkle
      ]

      for jointName in jointNames {
          do {
              let joint = try observation.recognizedPoint(jointName)
              if joint.confidence > 0.3 {
                  keypoints[jointName.rawValue.rawValue] = [
                      "x": joint.location.x,
                      "y": 1.0 - joint.location.y, // Flip Y coordinate
                      "confidence": joint.confidence
                  ]
              }
          } catch {
              // Joint not detected, skip
          }
      }

      return keypoints
  }

  private func analyzeBodyPose(observation: VNHumanBodyPoseObservation, keypoints: [String: Any]) -> [String: Any] {
      // Analyze pose for activity recognition
      let activity = self.classifyActivity(keypoints: keypoints)
      let posture = self.classifyPosture(keypoints: keypoints)
      let gesture = self.classifyBodyGesture(keypoints: keypoints)
      let energy = self.calculateEnergyLevel(keypoints: keypoints)

      return [
          "activity": activity,
          "posture": posture,
          "gesture": gesture,
          "energyLevel": energy,
          "isActionPose": energy > 0.6,
          "isStanding": posture == "standing",
          "armPosition": self.analyzeArmPosition(keypoints: keypoints),
          "legPosition": self.analyzeLegPosition(keypoints: keypoints)
      ]
  }

  private func extractHandKeypoints(from observation: VNHumanHandPoseObservation) -> [String: Any] {
      var keypoints: [String: Any] = [:]

      // Define hand joints we want to track
      let jointGroups: [VNHumanHandPoseObservation.JointsGroupName] = [
          .thumb, .indexFinger, .middleFinger, .ringFinger, .littleFinger
      ]

      for group in jointGroups {
          do {
              let joints = try observation.recognizedPoints(group)
              var groupKeypoints: [String: Any] = [:]

              for (jointName, joint) in joints {
                  if joint.confidence > 0.3 {
                      groupKeypoints[jointName.rawValue.rawValue] = [
                          "x": joint.location.x,
                          "y": 1.0 - joint.location.y, // Flip Y coordinate
                          "confidence": joint.confidence
                      ]
                  }
              }

              if !groupKeypoints.isEmpty {
                  keypoints[group.rawValue.rawValue] = groupKeypoints
              }
          } catch {
              // Joint group not detected, skip
          }
      }

      return keypoints
  }

  private func analyzeHandGesture(observation: VNHumanHandPoseObservation, keypoints: [String: Any]) -> [String: Any] {
      // Simplified gesture recognition
      let gestureType = self.classifyHandGesture(keypoints: keypoints)
      let confidence = self.calculateGestureConfidence(keypoints: keypoints, gestureType: gestureType)

      return [
          "type": gestureType,
          "confidence": confidence,
          "isPointing": gestureType == "pointing",
          "isWaving": gestureType == "waving",
          "isThumbsUp": gestureType == "thumbsUp",
          "isOpen": gestureType == "open",
          "isClosed": gestureType == "closed"
      ]
  }

  private func determineHandType(observation: VNHumanHandPoseObservation) -> String {
      // Simplified hand type determination based on position
      // In a real implementation, you'd use more sophisticated analysis
      return "unknown" // Could be "left" or "right" with better analysis
  }

  // MARK: - Activity Classification Methods

  private func classifyActivity(keypoints: [String: Any]) -> String {
      // Simplified activity classification based on keypoint positions
      let armMovement = self.calculateArmMovement(keypoints: keypoints)
      let legMovement = self.calculateLegMovement(keypoints: keypoints)
      let overallMovement = (armMovement + legMovement) / 2.0

      if overallMovement > 0.8 {
          return "highActivity" // Running, jumping, dancing
      } else if overallMovement > 0.5 {
          return "mediumActivity" // Walking, gesturing
      } else if overallMovement > 0.2 {
          return "lowActivity" // Standing, talking
      } else {
          return "static" // Sitting, lying down
      }
  }

  private func classifyPosture(keypoints: [String: Any]) -> String {
      // Analyze hip and shoulder positions to determine posture
      guard let leftHip = keypoints["leftHip"] as? [String: Any],
            let rightHip = keypoints["rightHip"] as? [String: Any],
            let leftShoulder = keypoints["leftShoulder"] as? [String: Any],
            let rightShoulder = keypoints["rightShoulder"] as? [String: Any] else {
          return "unknown"
      }

      let hipY = ((leftHip["y"] as? Double ?? 0.5) + (rightHip["y"] as? Double ?? 0.5)) / 2.0
      let shoulderY = ((leftShoulder["y"] as? Double ?? 0.5) + (rightShoulder["y"] as? Double ?? 0.5)) / 2.0

      let torsoHeight = abs(shoulderY - hipY)

      if torsoHeight > 0.3 {
          return "standing"
      } else if torsoHeight > 0.15 {
          return "sitting"
      } else {
          return "lying"
      }
  }

  private func classifyBodyGesture(keypoints: [String: Any]) -> String {
      // Analyze arm positions for gestures
      let armPosition = self.analyzeArmPosition(keypoints: keypoints)

      if armPosition.contains("raised") {
          return "celebration" // Arms up
      } else if armPosition.contains("extended") {
          return "pointing" // Arms extended
      } else if armPosition.contains("crossed") {
          return "defensive" // Arms crossed
      } else {
          return "neutral"
      }
  }

  private func calculateEnergyLevel(keypoints: [String: Any]) -> Double {
      // Calculate overall energy/movement level
      let armMovement = self.calculateArmMovement(keypoints: keypoints)
      let legMovement = self.calculateLegMovement(keypoints: keypoints)
      let headMovement = self.calculateHeadMovement(keypoints: keypoints)

      return (armMovement * 0.4 + legMovement * 0.4 + headMovement * 0.2)
  }

  private func analyzeArmPosition(keypoints: [String: Any]) -> String {
      // Simplified arm position analysis
      guard let leftShoulder = keypoints["leftShoulder"] as? [String: Any],
            let rightShoulder = keypoints["rightShoulder"] as? [String: Any],
            let leftWrist = keypoints["leftWrist"] as? [String: Any],
            let rightWrist = keypoints["rightWrist"] as? [String: Any] else {
          return "unknown"
      }

      let leftShoulderY = leftShoulder["y"] as? Double ?? 0.5
      let rightShoulderY = rightShoulder["y"] as? Double ?? 0.5
      let leftWristY = leftWrist["y"] as? Double ?? 0.5
      let rightWristY = rightWrist["y"] as? Double ?? 0.5

      let avgShoulderY = (leftShoulderY + rightShoulderY) / 2.0
      let avgWristY = (leftWristY + rightWristY) / 2.0

      if avgWristY < avgShoulderY - 0.1 {
          return "raised" // Arms up
      } else if avgWristY > avgShoulderY + 0.1 {
          return "lowered" // Arms down
      } else {
          return "neutral" // Arms at side
      }
  }

  private func analyzeLegPosition(keypoints: [String: Any]) -> String {
      // Simplified leg position analysis
      guard let leftHip = keypoints["leftHip"] as? [String: Any],
            let rightHip = keypoints["rightHip"] as? [String: Any],
            let leftAnkle = keypoints["leftAnkle"] as? [String: Any],
            let rightAnkle = keypoints["rightAnkle"] as? [String: Any] else {
          return "unknown"
      }

      let hipDistance = abs((leftHip["x"] as? Double ?? 0.5) - (rightHip["x"] as? Double ?? 0.5))
      let ankleDistance = abs((leftAnkle["x"] as? Double ?? 0.5) - (rightAnkle["x"] as? Double ?? 0.5))

      if ankleDistance > hipDistance * 1.5 {
          return "wide" // Wide stance
      } else if ankleDistance < hipDistance * 0.5 {
          return "narrow" // Narrow stance
      } else {
          return "normal" // Normal stance
      }
  }

  private func calculateArmMovement(keypoints: [String: Any]) -> Double {
      // Calculate arm movement energy
      guard let leftShoulder = keypoints["leftShoulder"] as? [String: Any],
            let rightShoulder = keypoints["rightShoulder"] as? [String: Any],
            let leftWrist = keypoints["leftWrist"] as? [String: Any],
            let rightWrist = keypoints["rightWrist"] as? [String: Any] else {
          return 0.0
      }

      // Calculate distance from shoulders to wrists
      let leftDistance = self.calculateDistance(
          from: leftShoulder,
          to: leftWrist
      )
      let rightDistance = self.calculateDistance(
          from: rightShoulder,
          to: rightWrist
      )

      // Normalize to 0-1 range (0.5 is typical arm length)
      return min(1.0, (leftDistance + rightDistance) / 1.0)
  }

  private func calculateLegMovement(keypoints: [String: Any]) -> Double {
      // Calculate leg movement energy
      guard let leftHip = keypoints["leftHip"] as? [String: Any],
            let rightHip = keypoints["rightHip"] as? [String: Any],
            let leftAnkle = keypoints["leftAnkle"] as? [String: Any],
            let rightAnkle = keypoints["rightAnkle"] as? [String: Any] else {
          return 0.0
      }

      let hipDistance = abs((leftHip["x"] as? Double ?? 0.5) - (rightHip["x"] as? Double ?? 0.5))
      let ankleDistance = abs((leftAnkle["x"] as? Double ?? 0.5) - (rightAnkle["x"] as? Double ?? 0.5))

      // Higher difference indicates more dynamic leg position
      return min(1.0, abs(ankleDistance - hipDistance) * 3.0)
  }

  private func calculateHeadMovement(keypoints: [String: Any]) -> Double {
      // Calculate head movement/tilt
      guard let nose = keypoints["nose"] as? [String: Any],
            let leftEar = keypoints["leftEar"] as? [String: Any],
            let rightEar = keypoints["rightEar"] as? [String: Any] else {
          return 0.0
      }

      let earDistance = abs((leftEar["x"] as? Double ?? 0.5) - (rightEar["x"] as? Double ?? 0.5))

      // Normalize ear distance (typical head width is ~0.1 in normalized coordinates)
      return min(1.0, earDistance * 10.0)
  }

  private func calculateDistance(from point1: [String: Any], to point2: [String: Any]) -> Double {
      let x1 = point1["x"] as? Double ?? 0.0
      let y1 = point1["y"] as? Double ?? 0.0
      let x2 = point2["x"] as? Double ?? 0.0
      let y2 = point2["y"] as? Double ?? 0.0

      return sqrt(pow(x2 - x1, 2) + pow(y2 - y1, 2))
  }

  private func classifyHandGesture(keypoints: [String: Any]) -> String {
      // Simplified hand gesture classification
      guard let thumb = keypoints["thumb"] as? [String: Any],
            let indexFinger = keypoints["indexFinger"] as? [String: Any] else {
          return "unknown"
      }

      // Very simplified gesture recognition
      if !thumb.isEmpty && !indexFinger.isEmpty {
          return "pointing" // Has thumb and index finger visible
      } else if !thumb.isEmpty {
          return "thumbsUp" // Thumb visible
      } else {
          return "closed" // Fingers not clearly visible
      }
  }

  private func calculateGestureConfidence(keypoints: [String: Any], gestureType: String) -> Double {
      // Calculate confidence based on keypoint visibility
      var totalConfidence = 0.0
      var keypointCount = 0

      for (_, fingerGroup) in keypoints {
          if let group = fingerGroup as? [String: Any] {
              for (_, joint) in group {
                  if let jointData = joint as? [String: Any],
                     let confidence = jointData["confidence"] as? Double {
                      totalConfidence += confidence
                      keypointCount += 1
                  }
              }
          }
      }

      return keypointCount > 0 ? totalConfidence / Double(keypointCount) : 0.0
  }

  // MARK: - Helper Methods to Avoid Complex Expressions

  private func calculateCounts(
      objectDetections: [[String: Any]],
      textDetections: [[String: Any]],
      faceDetections: [[String: Any]]
  ) -> [String: Any] {

      // Count people objects
      var peopleCount = 0
      for detection in objectDetections {
          if let label = detection["label"] as? String, label.contains("Person") {
              peopleCount += 1
          }
      }

      // Count animal objects
      var animalCount = 0
      for detection in objectDetections {
          if let label = detection["label"] as? String, label.contains("Animal") {
              animalCount += 1
          }
      }

      // Count high quality faces
      var highQualityFaces = 0
      for detection in faceDetections {
          if let quality = detection["quality"] as? [String: Any],
             let score = quality["score"] as? Double,
             score > 0.7 {
              highQualityFaces += 1
          }
      }

      return [
          "totalObjects": objectDetections.count,
          "totalText": textDetections.count,
          "totalFaces": faceDetections.count,
          "peopleCount": peopleCount,
          "animalCount": animalCount,
          "highQualityFaces": highQualityFaces
      ]
  }

  private func calculateFusionFlags(
      objectDetections: [[String: Any]],
      textDetections: [[String: Any]],
      faceDetections: [[String: Any]],
      interestScore: Double,
      visualMetrics: [String: Any]
  ) -> [String: Any] {

      // Check if has people
      var hasPeople = false
      for detection in objectDetections {
          if let label = detection["label"] as? String, label.contains("Person") {
              hasPeople = true
              break
          }
      }

      // Check if has high quality faces
      var hasHighQualityFaces = false
      for detection in faceDetections {
          if let quality = detection["quality"] as? [String: Any],
             let score = quality["score"] as? Double,
             score > 0.7 {
              hasHighQualityFaces = true
              break
          }
      }

      // Check overall quality
      let overallQuality = visualMetrics["overallQuality"] as? Double ?? 0.0
      let isHighQuality = overallQuality > 0.6

      // Check if has visual content
      let hasVisualContent = !objectDetections.isEmpty || !textDetections.isEmpty || !faceDetections.isEmpty

      return [
          "hasVisualContent": hasVisualContent,
          "hasHighConfidenceContent": interestScore > 0.5,
          "hasPeople": hasPeople,
          "hasFaces": !faceDetections.isEmpty,
          "hasText": !textDetections.isEmpty,
          "isHighQuality": isHighQuality,
          "isSocialMoment": faceDetections.count > 1,
          "hasHighQualityFaces": hasHighQualityFaces
      ]
  }

}
