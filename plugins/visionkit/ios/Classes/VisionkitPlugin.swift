import Flutter
import UIKit
import AVFoundation
import Vision

@available(iOS 15.0, *)
public class VisionKitPlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "visionkit", binaryMessenger: registrar.messenger())
    let instance = VisionKitPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    print("[VisionKitPlugin.swift] handle() called with method: \(call.method)")
    switch call.method {
    case "getPlatformVersion":
      result("iOS " + UIDevice.current.systemVersion)
    case "detectTextInVideo":
        handleDetectText(call: call, result: result)
    case "detectObjectsInVideo":
        handleDetectObjects(call: call, result: result)
    case "analyzeVideoComprehensively":
        handleComprehensiveAnalysis(call: call, result: result)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

  private func handleDetectText(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleDetectText() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let textDetections = try self.performTextDetectionSync(videoPath: videoPath)
              print("[VisionKitPlugin.swift] Detection finished, found \(textDetections.count) items.")
              DispatchQueue.main.async {
                  result(textDetections)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during detection: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "DETECTION_FAILED", message: "Failed to perform text detection: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performTextDetectionSync(videoPath: String) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performTextDetectionSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)
      
      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }
      
      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero
      
      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze more frames and spread them throughout the video
      let maxFrames = min(15, max(5, Int(durationInSeconds / 4)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames.")
      
      var allDetections: [[String: Any]] = []
      
      if maxFrames == 0 {
          return allDetections
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600) // Add small offset
              
              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let detections = try self.recognizeTextSync(in: cgImage, at: time)
                  
                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Found \(detections.count) texts")
                  
                  if !detections.isEmpty {
                      print("[VisionKitPlugin.swift] Detections: \(detections.map { $0["text"] ?? "unknown" })")
                      allDetections.append(contentsOf: detections)
                  }
              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }
      
      print("[VisionKitPlugin.swift] Total detections found: \(allDetections.count)")
      return allDetections
  }

  private func recognizeTextSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let request = VNRecognizeTextRequest()
      request.recognitionLevel = .accurate // Use accurate recognition for better detection
      request.usesLanguageCorrection = true // Enable language correction for better results
      
      // Support multiple languages
      request.recognitionLanguages = ["en-US", "en-GB", "es-ES", "fr-FR", "de-DE", "it-IT", "pt-BR", "zh-CN", "ja-JP"]
      
      // Enable automatic language detection (iOS 16.0+)
      if #available(iOS 16.0, *) {
          request.automaticallyDetectsLanguage = true
      }

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([request])

      guard let observations = request.results else {
          return []
      }

      print("[VisionKitPlugin.swift] Frame analysis: found \(observations.count) text observations")
      
      let validDetections = observations.compactMap { observation -> [String: Any]? in
          guard let topCandidate = observation.topCandidates(1).first else { 
              return nil 
          }
          
          print("[VisionKitPlugin.swift] Text candidate: '\(topCandidate.string)' confidence: \(topCandidate.confidence)")
          
          // Lower confidence threshold to catch more text
          guard topCandidate.confidence > 0.2 else {
              print("[VisionKitPlugin.swift] Rejected low confidence: \(topCandidate.confidence)")
              return nil
          }
          
          // Filter out very short or nonsensical text
          let trimmedText = topCandidate.string.trimmingCharacters(in: .whitespacesAndNewlines)
          guard trimmedText.count >= 2 else {
              print("[VisionKitPlugin.swift] Rejected short text: '\(trimmedText)'")
              return nil
          }
          
          return [
              "text": trimmedText,
              "confidence": topCandidate.confidence,
              "timestamp": time.seconds,
              "box": [
                  "x": observation.boundingBox.origin.x,
                  "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                  "width": observation.boundingBox.size.width,
                  "height": observation.boundingBox.size.height
              ]
          ]
      }
      
      print("[VisionKitPlugin.swift] Valid detections after filtering: \(validDetections.count)")
      return validDetections
  }

  private func handleDetectObjects(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleDetectObjects() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let objectDetections = try self.performObjectDetectionSync(videoPath: videoPath)
              print("[VisionKitPlugin.swift] Object detection finished, found \(objectDetections.count) items.")
              DispatchQueue.main.async {
                  result(objectDetections)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during object detection: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "DETECTION_FAILED", message: "Failed to perform object detection: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performObjectDetectionSync(videoPath: String) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performObjectDetectionSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)

      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }

      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero

      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze frames for object detection
      let maxFrames = min(20, max(8, Int(durationInSeconds / 3)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames for objects.")

      var allDetections: [[String: Any]] = []

      if maxFrames == 0 {
          return allDetections
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600)

              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let detections = try self.recognizeObjectsSync(in: cgImage, at: time)

                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Found \(detections.count) objects")

                  if !detections.isEmpty {
                      print("[VisionKitPlugin.swift] Object detections: \(detections.map { $0["label"] ?? "unknown" })")
                      allDetections.append(contentsOf: detections)
                  }
              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }

      print("[VisionKitPlugin.swift] Total object detections found: \(allDetections.count)")
      return allDetections
  }

  private func recognizeObjectsSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let request = VNRecognizeAnimalsRequest()
      let objectRequest = VNDetectHumanRectanglesRequest()

      // For iOS 17.0+ we can use more advanced object detection
      var requests: [VNRequest] = [request, objectRequest]

      if #available(iOS 17.0, *) {
          // Add more sophisticated object detection for newer iOS versions
          let advancedRequest = VNDetectHumanBodyPoseRequest()
          requests.append(advancedRequest)
      }

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform(requests)

      var allDetections: [[String: Any]] = []

      // Process animal detections
      if let animalObservations = request.results {
          print("[VisionKitPlugin.swift] Found \(animalObservations.count) animal observations")

          for observation in animalObservations {
              guard observation.confidence > 0.3 else { continue }

              let animalLabels = observation.labels.compactMap { label -> String? in
                  guard label.confidence > 0.3 else { return nil }
                  return label.identifier
              }

              if !animalLabels.isEmpty {
                  allDetections.append([
                      "label": "Animal: \(animalLabels.joined(separator: ", "))",
                      "confidence": observation.confidence,
                      "timestamp": time.seconds,
                      "box": [
                          "x": observation.boundingBox.origin.x,
                          "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                          "width": observation.boundingBox.size.width,
                          "height": observation.boundingBox.size.height
                      ],
                      "metadata": [
                          "type": "animal",
                          "labels": animalLabels,
                          "count": animalLabels.count
                      ]
                  ])
              }
          }
      }

      // Process human detections
      if let humanObservations = objectRequest.results {
          print("[VisionKitPlugin.swift] Found \(humanObservations.count) human observations")

          for observation in humanObservations {
              guard observation.confidence > 0.4 else { continue }

              allDetections.append([
                  "label": "Person",
                  "confidence": observation.confidence,
                  "timestamp": time.seconds,
                  "box": [
                      "x": observation.boundingBox.origin.x,
                      "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                      "width": observation.boundingBox.size.width,
                      "height": observation.boundingBox.size.height
                  ],
                  "metadata": [
                      "type": "person",
                      "upperBodyOnly": observation.upperBodyOnly
                  ]
              ])
          }
      }

      print("[VisionKitPlugin.swift] Valid object detections after filtering: \(allDetections.count)")
      return allDetections
  }

  private func handleComprehensiveAnalysis(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleComprehensiveAnalysis() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }

      let options = args["options"] as? [String: Any] ?? [:]
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath), options: \(options)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let analysisResults = try self.performComprehensiveAnalysisSync(videoPath: videoPath, options: options)
              print("[VisionKitPlugin.swift] Comprehensive analysis finished, found \(analysisResults.count) results.")
              DispatchQueue.main.async {
                  result(analysisResults)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during comprehensive analysis: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "ANALYSIS_FAILED", message: "Failed to perform comprehensive analysis: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performComprehensiveAnalysisSync(videoPath: String, options: [String: Any]) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performComprehensiveAnalysisSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)

      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }

      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero

      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze more frames for comprehensive analysis
      let maxFrames = min(25, max(10, Int(durationInSeconds / 2)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames comprehensively.")

      var allAnalysisResults: [[String: Any]] = []

      if maxFrames == 0 {
          return allAnalysisResults
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600)

              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let analysisResult = try self.performFrameAnalysis(image: cgImage, at: time, options: options)

                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Interest score \(analysisResult["interestScore"] ?? 0)")
                  allAnalysisResults.append(analysisResult)

              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }

      print("[VisionKitPlugin.swift] Total comprehensive analysis results: \(allAnalysisResults.count)")
      return allAnalysisResults
  }

  private func performFrameAnalysis(image: CGImage, at time: CMTime, options: [String: Any]) throws -> [String: Any] {
      // Perform all types of analysis on this frame
      let textDetections = try self.recognizeTextSync(in: image, at: time)
      let objectDetections = try self.recognizeObjectsSync(in: image, at: time)
      let sceneAnalysis = try self.analyzeScene(in: image, at: time)

      // Calculate interest score based on detections
      let interestScore = self.calculateInterestScore(
          textDetections: textDetections,
          objectDetections: objectDetections,
          sceneAnalysis: sceneAnalysis,
          options: options
      )

      return [
          "timestamp": time.seconds,
          "textDetections": textDetections,
          "objectDetections": objectDetections,
          "sceneAnalysis": sceneAnalysis,
          "interestScore": interestScore,
          "metadata": [
              "frameWidth": image.width,
              "frameHeight": image.height,
              "analysisTime": Date().timeIntervalSince1970
          ]
      ]
  }

  private func analyzeScene(in image: CGImage, at time: CMTime) throws -> [String: Any] {
      let request = VNClassifyImageRequest()

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([request])

      guard let observations = request.results else {
          return [
              "sceneType": "unknown",
              "confidence": 0.0,
              "timestamp": time.seconds,
              "attributes": [:]
          ]
      }

      // Get the top scene classification
      let topObservation = observations.first
      let sceneType = topObservation?.identifier ?? "unknown"
      let confidence = topObservation?.confidence ?? 0.0

      // Analyze lighting and other attributes
      let attributes = self.analyzeImageAttributes(image: image)

      return [
          "sceneType": sceneType,
          "confidence": confidence,
          "timestamp": time.seconds,
          "attributes": attributes
      ]
  }

  private func analyzeImageAttributes(image: CGImage) -> [String: Any] {
      // Basic image quality analysis
      let width = image.width
      let height = image.height
      let aspectRatio = Double(width) / Double(height)

      // Estimate brightness (simplified)
      let brightness = self.estimateBrightness(image: image)

      return [
          "width": width,
          "height": height,
          "aspectRatio": aspectRatio,
          "brightness": brightness,
          "quality": brightness > 0.3 ? "good" : "poor"
      ]
  }

  private func estimateBrightness(image: CGImage) -> Double {
      // Simplified brightness estimation
      // In a real implementation, you'd analyze pixel values
      return 0.5 // Placeholder value
  }

  private func calculateInterestScore(
      textDetections: [[String: Any]],
      objectDetections: [[String: Any]],
      sceneAnalysis: [String: Any],
      options: [String: Any]
  ) -> Double {
      var score: Double = 0.0

      // Base score from object detections
      let objectScore = self.calculateObjectScore(objectDetections: objectDetections)
      score += objectScore * 0.4

      // Score from text detections (important information)
      let textScore = self.calculateTextScore(textDetections: textDetections)
      score += textScore * 0.2

      // Scene quality and type score
      let sceneScore = self.calculateSceneScore(sceneAnalysis: sceneAnalysis)
      score += sceneScore * 0.3

      // Diversity bonus (multiple types of content)
      let diversityScore = self.calculateDiversityScore(
          hasText: !textDetections.isEmpty,
          hasObjects: !objectDetections.isEmpty,
          hasScene: sceneAnalysis["confidence"] as? Double ?? 0.0 > 0.3
      )
      score += diversityScore * 0.1

      // Normalize to 0-1 range
      return min(1.0, max(0.0, score))
  }

  private func calculateObjectScore(objectDetections: [[String: Any]]) -> Double {
      guard !objectDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0
      var personCount = 0
      var animalCount = 0

      for detection in objectDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let label = detection["label"] as? String ?? ""

          if label.contains("Person") {
              personCount += 1
              score += confidence * 0.8 // People are very interesting
          } else if label.contains("Animal") {
              animalCount += 1
              score += confidence * 0.6 // Animals are interesting
          } else {
              score += confidence * 0.3 // Other objects
          }
      }

      // Bonus for multiple people (social interaction)
      if personCount > 1 {
          score += 0.3
      }

      // Bonus for animals
      if animalCount > 0 {
          score += 0.2
      }

      return min(1.0, score)
  }

  private func calculateTextScore(textDetections: [[String: Any]]) -> Double {
      guard !textDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0

      for detection in textDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let text = detection["text"] as? String ?? ""

          // Higher score for longer, more confident text
          let lengthBonus = min(0.5, Double(text.count) / 20.0)
          score += (confidence + lengthBonus) * 0.5
      }

      return min(1.0, score / Double(textDetections.count))
  }

  private func calculateSceneScore(sceneAnalysis: [String: Any]) -> Double {
      let confidence = sceneAnalysis["confidence"] as? Double ?? 0.0
      let sceneType = sceneAnalysis["sceneType"] as? String ?? ""
      let attributes = sceneAnalysis["attributes"] as? [String: Any] ?? [:]

      var score = confidence

      // Bonus for high-quality scenes
      if let quality = attributes["quality"] as? String, quality == "good" {
          score += 0.2
      }

      // Bonus for interesting scene types
      let interestingScenes = ["outdoor", "sports", "party", "performance", "celebration"]
      if interestingScenes.contains(where: { sceneType.lowercased().contains($0) }) {
          score += 0.3
      }

      return min(1.0, score)
  }

  private func calculateDiversityScore(hasText: Bool, hasObjects: Bool, hasScene: Bool) -> Double {
      let components = [hasText, hasObjects, hasScene].filter { $0 }.count
      return Double(components) / 3.0
  }
}
