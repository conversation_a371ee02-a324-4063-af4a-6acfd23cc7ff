import Flutter
import UIKit
import AVFoundation
import Vision

@available(iOS 15.0, *)
public class VisionKitPlugin: NSObject, FlutterPlugin {
  public static func register(with registrar: FlutterPluginRegistrar) {
    let channel = FlutterMethodChannel(name: "visionkit", binaryMessenger: registrar.messenger())
    let instance = VisionKitPlugin()
    registrar.addMethodCallDelegate(instance, channel: channel)
  }

  public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
    print("[VisionKitPlugin.swift] handle() called with method: \(call.method)")
    switch call.method {
    case "getPlatformVersion":
      result("iOS " + UIDevice.current.systemVersion)
    case "detectTextInVideo":
        handleDetectText(call: call, result: result)
    case "detectObjectsInVideo":
        handleDetectObjects(call: call, result: result)
    case "analyzeVideoComprehensively":
        handleComprehensiveAnalysis(call: call, result: result)
    default:
      result(FlutterMethodNotImplemented)
    }
  }

  private func handleDetectText(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleDetectText() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let textDetections = try self.performTextDetectionSync(videoPath: videoPath)
              print("[VisionKitPlugin.swift] Detection finished, found \(textDetections.count) items.")
              DispatchQueue.main.async {
                  result(textDetections)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during detection: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "DETECTION_FAILED", message: "Failed to perform text detection: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performTextDetectionSync(videoPath: String) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performTextDetectionSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)
      
      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }
      
      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero
      
      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze more frames and spread them throughout the video
      let maxFrames = min(15, max(5, Int(durationInSeconds / 4)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames.")
      
      var allDetections: [[String: Any]] = []
      
      if maxFrames == 0 {
          return allDetections
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600) // Add small offset
              
              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let detections = try self.recognizeTextSync(in: cgImage, at: time)
                  
                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Found \(detections.count) texts")
                  
                  if !detections.isEmpty {
                      print("[VisionKitPlugin.swift] Detections: \(detections.map { $0["text"] ?? "unknown" })")
                      allDetections.append(contentsOf: detections)
                  }
              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }
      
      print("[VisionKitPlugin.swift] Total detections found: \(allDetections.count)")
      return allDetections
  }

  private func recognizeTextSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let request = VNRecognizeTextRequest()
      request.recognitionLevel = .accurate // Use accurate recognition for better detection
      request.usesLanguageCorrection = true // Enable language correction for better results
      
      // Support multiple languages
      request.recognitionLanguages = ["en-US", "en-GB", "es-ES", "fr-FR", "de-DE", "it-IT", "pt-BR", "zh-CN", "ja-JP"]
      
      // Enable automatic language detection (iOS 16.0+)
      if #available(iOS 16.0, *) {
          request.automaticallyDetectsLanguage = true
      }

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([request])

      guard let observations = request.results else {
          return []
      }

      print("[VisionKitPlugin.swift] Frame analysis: found \(observations.count) text observations")
      
      let validDetections = observations.compactMap { observation -> [String: Any]? in
          guard let topCandidate = observation.topCandidates(1).first else { 
              return nil 
          }
          
          print("[VisionKitPlugin.swift] Text candidate: '\(topCandidate.string)' confidence: \(topCandidate.confidence)")
          
          // Lower confidence threshold to catch more text
          guard topCandidate.confidence > 0.2 else {
              print("[VisionKitPlugin.swift] Rejected low confidence: \(topCandidate.confidence)")
              return nil
          }
          
          // Filter out very short or nonsensical text
          let trimmedText = topCandidate.string.trimmingCharacters(in: .whitespacesAndNewlines)
          guard trimmedText.count >= 2 else {
              print("[VisionKitPlugin.swift] Rejected short text: '\(trimmedText)'")
              return nil
          }
          
          return [
              "text": trimmedText,
              "confidence": topCandidate.confidence,
              "timestamp": time.seconds,
              "box": [
                  "x": observation.boundingBox.origin.x,
                  "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                  "width": observation.boundingBox.size.width,
                  "height": observation.boundingBox.size.height
              ]
          ]
      }
      
      print("[VisionKitPlugin.swift] Valid detections after filtering: \(validDetections.count)")
      return validDetections
  }

  private func handleDetectObjects(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleDetectObjects() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let objectDetections = try self.performObjectDetectionSync(videoPath: videoPath)
              print("[VisionKitPlugin.swift] Object detection finished, found \(objectDetections.count) items.")
              DispatchQueue.main.async {
                  result(objectDetections)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during object detection: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "DETECTION_FAILED", message: "Failed to perform object detection: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performObjectDetectionSync(videoPath: String) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performObjectDetectionSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)

      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }

      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero

      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze frames for object detection
      let maxFrames = min(20, max(8, Int(durationInSeconds / 3)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames for objects.")

      var allDetections: [[String: Any]] = []

      if maxFrames == 0 {
          return allDetections
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600)

              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let detections = try self.recognizeObjectsSync(in: cgImage, at: time)

                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Found \(detections.count) objects")

                  if !detections.isEmpty {
                      print("[VisionKitPlugin.swift] Object detections: \(detections.map { $0["label"] ?? "unknown" })")
                      allDetections.append(contentsOf: detections)
                  }
              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }

      print("[VisionKitPlugin.swift] Total object detections found: \(allDetections.count)")
      return allDetections
  }

  private func recognizeObjectsSync(in image: CGImage, at time: CMTime) throws -> [[String: Any]] {
      let request = VNRecognizeAnimalsRequest()
      let objectRequest = VNDetectHumanRectanglesRequest()

      // For iOS 17.0+ we can use more advanced object detection
      var requests: [VNRequest] = [request, objectRequest]

      if #available(iOS 17.0, *) {
          // Add more sophisticated object detection for newer iOS versions
          let advancedRequest = VNDetectHumanBodyPoseRequest()
          requests.append(advancedRequest)
      }

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform(requests)

      var allDetections: [[String: Any]] = []

      // Process animal detections
      if let animalObservations = request.results {
          print("[VisionKitPlugin.swift] Found \(animalObservations.count) animal observations")

          for observation in animalObservations {
              guard observation.confidence > 0.3 else { continue }

              let animalLabels = observation.labels.compactMap { label -> String? in
                  guard label.confidence > 0.3 else { return nil }
                  return label.identifier
              }

              if !animalLabels.isEmpty {
                  allDetections.append([
                      "label": "Animal: \(animalLabels.joined(separator: ", "))",
                      "confidence": observation.confidence,
                      "timestamp": time.seconds,
                      "box": [
                          "x": observation.boundingBox.origin.x,
                          "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                          "width": observation.boundingBox.size.width,
                          "height": observation.boundingBox.size.height
                      ],
                      "metadata": [
                          "type": "animal",
                          "labels": animalLabels,
                          "count": animalLabels.count
                      ]
                  ])
              }
          }
      }

      // Process human detections
      if let humanObservations = objectRequest.results {
          print("[VisionKitPlugin.swift] Found \(humanObservations.count) human observations")

          for observation in humanObservations {
              guard observation.confidence > 0.4 else { continue }

              allDetections.append([
                  "label": "Person",
                  "confidence": observation.confidence,
                  "timestamp": time.seconds,
                  "box": [
                      "x": observation.boundingBox.origin.x,
                      "y": 1.0 - observation.boundingBox.origin.y - observation.boundingBox.height,
                      "width": observation.boundingBox.size.width,
                      "height": observation.boundingBox.size.height
                  ],
                  "metadata": [
                      "type": "person",
                      "upperBodyOnly": observation.upperBodyOnly
                  ]
              ])
          }
      }

      print("[VisionKitPlugin.swift] Valid object detections after filtering: \(allDetections.count)")
      return allDetections
  }

  private func handleComprehensiveAnalysis(call: FlutterMethodCall, result: @escaping FlutterResult) {
      print("[VisionKitPlugin.swift] handleComprehensiveAnalysis() called")
      guard let args = call.arguments as? [String: Any],
            let videoPath = args["videoPath"] as? String else {
          let errorMsg = "Invalid arguments"
          print("[VisionKitPlugin.swift] Error: \(errorMsg)")
          result(FlutterError(code: "INVALID_ARGUMENTS", message: errorMsg, details: nil))
          return
      }

      let options = args["options"] as? [String: Any] ?? [:]
      print("[VisionKitPlugin.swift] Received videoPath: \(videoPath), options: \(options)")

      // Use a background queue for processing to avoid blocking the main thread
      DispatchQueue.global(qos: .userInitiated).async {
          do {
              let analysisResults = try self.performComprehensiveAnalysisSync(videoPath: videoPath, options: options)
              print("[VisionKitPlugin.swift] Comprehensive analysis finished, found \(analysisResults.count) results.")
              DispatchQueue.main.async {
                  result(analysisResults)
              }
          } catch {
              print("[VisionKitPlugin.swift] Caught error during comprehensive analysis: \(error.localizedDescription)")
              DispatchQueue.main.async {
                  result(FlutterError(code: "ANALYSIS_FAILED", message: "Failed to perform comprehensive analysis: \(error.localizedDescription)", details: nil))
              }
          }
      }
  }

  private func performComprehensiveAnalysisSync(videoPath: String, options: [String: Any]) throws -> [[String: Any]] {
      print("[VisionKitPlugin.swift] performComprehensiveAnalysisSync() starting")
      let videoURL = URL(fileURLWithPath: videoPath)
      let asset = AVAsset(url: videoURL)

      // Check if asset is valid
      guard asset.isPlayable else {
          print("[VisionKitPlugin.swift] Error: Asset is not playable")
          throw NSError(domain: "VisionKitPlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
      }

      guard let videoTrack = asset.tracks(withMediaType: .video).first else {
          print("[VisionKitPlugin.swift] Error: No video tracks found")
          throw NSError(domain: "VisionKitPlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
      }

      let imageGenerator = AVAssetImageGenerator(asset: asset)
      imageGenerator.appliesPreferredTrackTransform = true
      imageGenerator.requestedTimeToleranceBefore = CMTime.zero
      imageGenerator.requestedTimeToleranceAfter = CMTime.zero

      let duration = asset.duration
      let durationInSeconds = CMTimeGetSeconds(duration)
      print("[VisionKitPlugin.swift] Asset duration: \(durationInSeconds)s")

      // Analyze more frames for comprehensive analysis
      let maxFrames = min(25, max(10, Int(durationInSeconds / 2)))
      print("[VisionKitPlugin.swift] Analyzing \(maxFrames) frames comprehensively.")

      var allAnalysisResults: [[String: Any]] = []

      if maxFrames == 0 {
          return allAnalysisResults
      }

      for i in 0..<maxFrames {
          autoreleasepool {
              let timeInterval = durationInSeconds / Double(maxFrames)
              let time = CMTime(seconds: Double(i) * timeInterval + 0.5, preferredTimescale: 600)

              do {
                  let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                  let analysisResult = try self.performFrameAnalysis(image: cgImage, at: time, options: options)

                  print("[VisionKitPlugin.swift] Frame \(i) at \(time.seconds)s: Interest score \(analysisResult["interestScore"] ?? 0)")
                  allAnalysisResults.append(analysisResult)

              } catch {
                  print("[VisionKitPlugin.swift] Error processing frame \(i): \(error)")
                  // Continue with other frames even if one fails
              }
          }
      }

      print("[VisionKitPlugin.swift] Total comprehensive analysis results: \(allAnalysisResults.count)")

      // Log RAW JSON data being returned to Flutter
      print("=== RAW SWIFT JSON DATA BEING RETURNED ===")
      for (index, result) in allAnalysisResults.enumerated() {
          print("--- RAW RESULT \(index + 1) ---")
          if let jsonData = try? JSONSerialization.data(withJSONObject: result, options: .prettyPrinted),
             let jsonString = String(data: jsonData, encoding: .utf8) {
              print(jsonString)
          } else {
              print("Raw dictionary: \(result)")
          }
          print("--- END RAW RESULT \(index + 1) ---")
      }
      print("=== END RAW SWIFT JSON DATA ===")

      return allAnalysisResults
  }

  private func performFrameAnalysis(image: CGImage, at time: CMTime, options: [String: Any]) throws -> [String: Any] {
      // Perform all types of analysis on this frame
      let textDetections = try self.recognizeTextSync(in: image, at: time)
      let objectDetections = try self.recognizeObjectsSync(in: image, at: time)
      let sceneAnalysis = try self.analyzeScene(in: image, at: time)

      // Calculate additional metrics for future fusion
      let visualMetrics = self.calculateVisualMetrics(image: image)
      let contentDensity = self.calculateContentDensity(
          textCount: textDetections.count,
          objectCount: objectDetections.count,
          imageSize: CGSize(width: image.width, height: image.height)
      )
      let temporalContext = self.calculateTemporalContext(timestamp: time.seconds)

      // Calculate interest score based on detections
      let interestScore = self.calculateInterestScore(
          textDetections: textDetections,
          objectDetections: objectDetections,
          sceneAnalysis: sceneAnalysis,
          options: options
      )

      // Enhanced JSON with rich metadata for future needs
      return [
          "timestamp": time.seconds,
          "textDetections": textDetections,
          "objectDetections": objectDetections,
          "sceneAnalysis": sceneAnalysis,
          "interestScore": interestScore,

          // Enhanced metadata for multi-modal fusion
          "metadata": [
              // Basic frame info
              "frameWidth": image.width,
              "frameHeight": image.height,
              "analysisTime": Date().timeIntervalSince1970,

              // Visual quality metrics
              "visualMetrics": visualMetrics,

              // Content density for highlight scoring
              "contentDensity": contentDensity,

              // Temporal context for sequence analysis
              "temporalContext": temporalContext,

              // Counts for quick filtering
              "counts": [
                  "totalObjects": objectDetections.count,
                  "totalText": textDetections.count,
                  "peopleCount": objectDetections.filter { ($0["label"] as? String)?.contains("Person") == true }.count,
                  "animalCount": objectDetections.filter { ($0["label"] as? String)?.contains("Animal") == true }.count
              ],

              // Fusion readiness flags
              "fusionReady": [
                  "hasVisualContent": !objectDetections.isEmpty || !textDetections.isEmpty,
                  "hasHighConfidenceContent": interestScore > 0.5,
                  "hasPeople": objectDetections.contains { ($0["label"] as? String)?.contains("Person") == true },
                  "hasText": !textDetections.isEmpty,
                  "isHighQuality": visualMetrics["overallQuality"] as? Double ?? 0.0 > 0.6
              ]
          ]
      ]
  }

  private func analyzeScene(in image: CGImage, at time: CMTime) throws -> [String: Any] {
      let request = VNClassifyImageRequest()

      let handler = VNImageRequestHandler(cgImage: image, options: [:])
      try handler.perform([request])

      guard let observations = request.results else {
          return [
              "sceneType": "unknown",
              "confidence": 0.0,
              "timestamp": time.seconds,
              "attributes": [:]
          ]
      }

      // Get the top scene classification
      let topObservation = observations.first
      let sceneType = topObservation?.identifier ?? "unknown"
      let confidence = topObservation?.confidence ?? 0.0

      // Analyze lighting and other attributes
      let attributes = self.analyzeImageAttributes(image: image)

      print("[VisionKitPlugin.swift] Scene analysis - Type: \(sceneType), Confidence: \(confidence)")

      return [
          "sceneType": sceneType,
          "confidence": confidence,
          "timestamp": time.seconds,
          "attributes": attributes
      ]
  }

  private func analyzeImageAttributes(image: CGImage) -> [String: Any] {
      // Basic image quality analysis
      let width = image.width
      let height = image.height
      let aspectRatio = Double(width) / Double(height)

      // Estimate brightness (simplified)
      let brightness = self.estimateBrightness(image: image)

      return [
          "width": width,
          "height": height,
          "aspectRatio": aspectRatio,
          "brightness": brightness,
          "quality": brightness > 0.3 ? "good" : "poor"
      ]
  }

  private func estimateBrightness(image: CGImage) -> Double {
      // Real brightness estimation using Core Image
      let ciImage = CIImage(cgImage: image)
      let context = CIContext()

      // Use Core Image to calculate average brightness
      let filter = CIFilter(name: "CIAreaAverage")!
      filter.setValue(ciImage, forKey: kCIInputImageKey)
      filter.setValue(CIVector(cgRect: ciImage.extent), forKey: kCIInputExtentKey)

      guard let outputImage = filter.outputImage else {
          return 0.5 // Fallback if analysis fails
      }

      var bitmap = [UInt8](repeating: 0, count: 4)
      context.render(outputImage, toBitmap: &bitmap, rowBytes: 4, bounds: CGRect(x: 0, y: 0, width: 1, height: 1), format: .RGBA8, colorSpace: nil)

      // Calculate brightness from RGB values
      let brightness = (Double(bitmap[0]) + Double(bitmap[1]) + Double(bitmap[2])) / (3.0 * 255.0)
      return brightness
  }

  private func calculateInterestScore(
      textDetections: [[String: Any]],
      objectDetections: [[String: Any]],
      sceneAnalysis: [String: Any],
      options: [String: Any]
  ) -> Double {
      var score: Double = 0.0

      // Base score from object detections
      let objectScore = self.calculateObjectScore(objectDetections: objectDetections)
      score += objectScore * 0.4

      // Score from text detections (important information)
      let textScore = self.calculateTextScore(textDetections: textDetections)
      score += textScore * 0.2

      // Scene quality and type score
      let sceneScore = self.calculateSceneScore(sceneAnalysis: sceneAnalysis)
      score += sceneScore * 0.3

      // Diversity bonus (multiple types of content)
      let diversityScore = self.calculateDiversityScore(
          hasText: !textDetections.isEmpty,
          hasObjects: !objectDetections.isEmpty,
          hasScene: sceneAnalysis["confidence"] as? Double ?? 0.0 > 0.3
      )
      score += diversityScore * 0.1

      // Normalize to 0-1 range
      let finalScore = min(1.0, max(0.0, score))
      print("[VisionKitPlugin.swift] Interest score calculation - Objects: \(objectDetections.count), Text: \(textDetections.count), Final score: \(finalScore)")
      return finalScore
  }

  private func calculateObjectScore(objectDetections: [[String: Any]]) -> Double {
      guard !objectDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0
      var personCount = 0
      var animalCount = 0

      for detection in objectDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let label = detection["label"] as? String ?? ""

          if label.contains("Person") {
              personCount += 1
              score += confidence * 0.8 // People are very interesting
          } else if label.contains("Animal") {
              animalCount += 1
              score += confidence * 0.6 // Animals are interesting
          } else {
              score += confidence * 0.3 // Other objects
          }
      }

      // Bonus for multiple people (social interaction)
      if personCount > 1 {
          score += 0.3
      }

      // Bonus for animals
      if animalCount > 0 {
          score += 0.2
      }

      return min(1.0, score)
  }

  private func calculateTextScore(textDetections: [[String: Any]]) -> Double {
      guard !textDetections.isEmpty else { return 0.0 }

      var score: Double = 0.0

      for detection in textDetections {
          let confidence = detection["confidence"] as? Double ?? 0.0
          let text = detection["text"] as? String ?? ""

          // Higher score for longer, more confident text
          let lengthBonus = min(0.5, Double(text.count) / 20.0)
          score += (confidence + lengthBonus) * 0.5
      }

      return min(1.0, score / Double(textDetections.count))
  }

  private func calculateSceneScore(sceneAnalysis: [String: Any]) -> Double {
      let confidence = sceneAnalysis["confidence"] as? Double ?? 0.0
      let sceneType = sceneAnalysis["sceneType"] as? String ?? ""
      let attributes = sceneAnalysis["attributes"] as? [String: Any] ?? [:]

      var score = confidence

      // Bonus for high-quality scenes
      if let quality = attributes["quality"] as? String, quality == "good" {
          score += 0.2
      }

      // Bonus for interesting scene types
      let interestingScenes = ["outdoor", "sports", "party", "performance", "celebration"]
      if interestingScenes.contains(where: { sceneType.lowercased().contains($0) }) {
          score += 0.3
      }

      return min(1.0, score)
  }

  private func calculateDiversityScore(hasText: Bool, hasObjects: Bool, hasScene: Bool) -> Double {
      let components = [hasText, hasObjects, hasScene].filter { $0 }.count
      return Double(components) / 3.0
  }

  // MARK: - Enhanced Metrics for Future Fusion

  private func calculateVisualMetrics(image: CGImage) -> [String: Any] {
      let brightness = self.estimateBrightness(image: image)
      let aspectRatio = Double(image.width) / Double(image.height)

      // Calculate sharpness estimate (simplified)
      let sharpness = self.estimateSharpness(image: image)

      // Overall quality score
      let overallQuality = (brightness * 0.4 + sharpness * 0.6)

      return [
          "brightness": brightness,
          "sharpness": sharpness,
          "aspectRatio": aspectRatio,
          "overallQuality": overallQuality,
          "resolution": [
              "width": image.width,
              "height": image.height,
              "megapixels": Double(image.width * image.height) / 1_000_000.0
          ]
      ]
  }

  private func calculateContentDensity(textCount: Int, objectCount: Int, imageSize: CGSize) -> [String: Any] {
      let totalPixels = imageSize.width * imageSize.height
      let contentItems = Double(textCount + objectCount)
      let density = contentItems / (totalPixels / 1_000_000.0) // Items per megapixel

      return [
          "itemsPerMegapixel": density,
          "totalContentItems": Int(contentItems),
          "densityCategory": density > 5.0 ? "high" : (density > 2.0 ? "medium" : "low"),
          "hasRichContent": contentItems >= 3
      ]
  }

  private func calculateTemporalContext(timestamp: Double) -> [String: Any] {
      return [
          "timestamp": timestamp,
          "timeCategory": self.categorizeTimestamp(timestamp),
          "isEarlyInVideo": timestamp < 10.0,
          "isLateInVideo": false, // Would need video duration to calculate
          "roundedSecond": Int(timestamp.rounded()),
          "fractionalSecond": timestamp.truncatingRemainder(dividingBy: 1.0)
      ]
  }

  private func categorizeTimestamp(_ timestamp: Double) -> String {
      if timestamp < 5.0 { return "opening" }
      else if timestamp < 30.0 { return "early" }
      else if timestamp < 120.0 { return "middle" }
      else { return "late" }
  }

  private func estimateSharpness(image: CGImage) -> Double {
      // Simplified sharpness estimation
      // In a real implementation, you'd analyze edge detection or frequency domain
      let width = image.width
      let height = image.height

      // Assume higher resolution = potentially sharper (very simplified)
      let resolutionFactor = min(1.0, Double(width * height) / (1920.0 * 1080.0))

      // Return a reasonable estimate between 0.3 and 0.9
      return 0.3 + (resolutionFactor * 0.6)
  }

}
