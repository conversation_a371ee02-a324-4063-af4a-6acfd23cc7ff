import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// A class to hold the results of a text detection operation.
class TextDetection {
  /// The detected text string.
  final String text;

  /// The confidence of the detection, from 0.0 to 1.0.
  final double confidence;

  /// The timestamp in the video where the text was detected.
  final double timestamp;

  /// The bounding box of the detected text.
  final Rect box;

  TextDetection({
    required this.text,
    required this.confidence,
    required this.timestamp,
    required this.box,
  });

  /// Creates a [TextDetection] from a map, typically from the platform channel.
  factory TextDetection.fromMap(Map<dynamic, dynamic> map) {
    final boxMap = map['box'] as Map<dynamic, dynamic>;
    return TextDetection(
      text: map['text'] as String,
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: (map['timestamp'] as num).toDouble(),
      box: Rect.fromLTWH(
        (boxMap['x'] as num).toDouble(),
        (boxMap['y'] as num).toDouble(),
        (boxMap['width'] as num).toDouble(),
        (boxMap['height'] as num).toDouble(),
      ),
    );
  }

  @override
  String toString() {
    return 'TextDetection(text: $text, confidence: $confidence, timestamp: $timestamp)';
  }
}

/// A class to hold the results of object detection operations.
class ObjectDetection {
  /// The type/label of the detected object.
  final String label;

  /// The confidence of the detection, from 0.0 to 1.0.
  final double confidence;

  /// The timestamp in the video where the object was detected.
  final double timestamp;

  /// The bounding box of the detected object.
  final Rect box;

  /// Additional metadata about the object (size, attributes, etc.).
  final Map<String, dynamic> metadata;

  ObjectDetection({
    required this.label,
    required this.confidence,
    required this.timestamp,
    required this.box,
    this.metadata = const {},
  });

  /// Creates an [ObjectDetection] from a map, typically from the platform channel.
  factory ObjectDetection.fromMap(Map<dynamic, dynamic> map) {
    final boxMap = map['box'] as Map<dynamic, dynamic>;
    return ObjectDetection(
      label: map['label'] as String,
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: (map['timestamp'] as num).toDouble(),
      box: Rect.fromLTWH(
        (boxMap['x'] as num).toDouble(),
        (boxMap['y'] as num).toDouble(),
        (boxMap['width'] as num).toDouble(),
        (boxMap['height'] as num).toDouble(),
      ),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'ObjectDetection(label: $label, confidence: $confidence, timestamp: $timestamp)';
  }
}

/// A class to hold face detection and analysis results.
class FaceDetection {
  /// The confidence of the face detection, from 0.0 to 1.0.
  final double confidence;

  /// The timestamp in the video where the face was detected.
  final double timestamp;

  /// The bounding box of the detected face.
  final Rect box;

  /// Face landmarks (eyes, nose, mouth positions) if available.
  final Map<String, dynamic>? landmarks;

  /// Face quality metrics for photo-worthiness.
  final Map<String, dynamic> quality;

  /// Additional face analysis (age, emotion estimates, etc.).
  final Map<String, dynamic> analysis;

  FaceDetection({
    required this.confidence,
    required this.timestamp,
    required this.box,
    this.landmarks,
    this.quality = const {},
    this.analysis = const {},
  });

  /// Creates a [FaceDetection] from a map, typically from the platform channel.
  factory FaceDetection.fromMap(Map<dynamic, dynamic> map) {
    final boxMap = map['box'] as Map<dynamic, dynamic>;
    return FaceDetection(
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: (map['timestamp'] as num).toDouble(),
      box: Rect.fromLTWH(
        (boxMap['x'] as num).toDouble(),
        (boxMap['y'] as num).toDouble(),
        (boxMap['width'] as num).toDouble(),
        (boxMap['height'] as num).toDouble(),
      ),
      landmarks: map['landmarks'] != null
          ? Map<String, dynamic>.from(map['landmarks'])
          : null,
      quality: Map<String, dynamic>.from(map['quality'] ?? {}),
      analysis: Map<String, dynamic>.from(map['analysis'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'FaceDetection(confidence: $confidence, timestamp: $timestamp, quality: ${quality['score'] ?? 'unknown'})';
  }
}

/// A class to hold body pose detection and analysis results.
class BodyPoseDetection {
  /// The confidence of the body pose detection, from 0.0 to 1.0.
  final double confidence;

  /// The timestamp in the video where the pose was detected.
  final double timestamp;

  /// Body keypoints (joints) with their positions and confidence.
  final Map<String, dynamic> keypoints;

  /// Pose analysis (activity, gesture, posture classification).
  final Map<String, dynamic> analysis;

  /// Additional metadata about the pose detection.
  final Map<String, dynamic> metadata;

  BodyPoseDetection({
    required this.confidence,
    required this.timestamp,
    this.keypoints = const {},
    this.analysis = const {},
    this.metadata = const {},
  });

  /// Creates a [BodyPoseDetection] from a map, typically from the platform channel.
  factory BodyPoseDetection.fromMap(Map<dynamic, dynamic> map) {
    return BodyPoseDetection(
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: (map['timestamp'] as num).toDouble(),
      keypoints: Map<String, dynamic>.from(map['keypoints'] ?? {}),
      analysis: Map<String, dynamic>.from(map['analysis'] ?? {}),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'BodyPoseDetection(confidence: $confidence, timestamp: $timestamp, activity: ${analysis['activity'] ?? 'unknown'})';
  }
}

/// A class to hold hand pose detection and gesture analysis results.
class HandPoseDetection {
  /// The confidence of the hand pose detection, from 0.0 to 1.0.
  final double confidence;

  /// The timestamp in the video where the hand pose was detected.
  final double timestamp;

  /// Hand type ('left' or 'right').
  final String handType;

  /// Hand keypoints (finger joints) with their positions.
  final Map<String, dynamic> keypoints;

  /// Gesture analysis (pointing, waving, thumbs up, etc.).
  final Map<String, dynamic> gesture;

  /// Additional metadata about the hand detection.
  final Map<String, dynamic> metadata;

  HandPoseDetection({
    required this.confidence,
    required this.timestamp,
    required this.handType,
    this.keypoints = const {},
    this.gesture = const {},
    this.metadata = const {},
  });

  /// Creates a [HandPoseDetection] from a map, typically from the platform channel.
  factory HandPoseDetection.fromMap(Map<dynamic, dynamic> map) {
    return HandPoseDetection(
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: (map['timestamp'] as num).toDouble(),
      handType: map['handType'] as String? ?? 'unknown',
      keypoints: Map<String, dynamic>.from(map['keypoints'] ?? {}),
      gesture: Map<String, dynamic>.from(map['gesture'] ?? {}),
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'HandPoseDetection($handType hand, confidence: $confidence, gesture: ${gesture['type'] ?? 'unknown'})';
  }
}

/// A class to hold the results of scene analysis operations.
class SceneAnalysis {
  /// The scene classification label.
  final String sceneType;

  /// The confidence of the classification, from 0.0 to 1.0.
  final double confidence;

  /// The timestamp in the video where the scene was analyzed.
  final double timestamp;

  /// Additional scene attributes (lighting, setting, activity type, etc.).
  final Map<String, dynamic> attributes;

  SceneAnalysis({
    required this.sceneType,
    required this.confidence,
    required this.timestamp,
    this.attributes = const {},
  });

  /// Creates a [SceneAnalysis] from a map, typically from the platform channel.
  factory SceneAnalysis.fromMap(Map<dynamic, dynamic> map) {
    return SceneAnalysis(
      sceneType: map['sceneType'] as String,
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: (map['timestamp'] as num).toDouble(),
      attributes: Map<String, dynamic>.from(map['attributes'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'SceneAnalysis(sceneType: $sceneType, confidence: $confidence, timestamp: $timestamp)';
  }
}

/// A comprehensive analysis result combining multiple Vision Kit features.
class VisionAnalysisResult {
  /// The timestamp of this analysis.
  final double timestamp;

  /// Text detections found at this timestamp.
  final List<TextDetection> textDetections;

  /// Object detections found at this timestamp.
  final List<ObjectDetection> objectDetections;

  /// Face detections found at this timestamp.
  final List<FaceDetection> faceDetections;

  /// Body pose detections found at this timestamp.
  final List<BodyPoseDetection> bodyPoseDetections;

  /// Hand pose detections found at this timestamp.
  final List<HandPoseDetection> handPoseDetections;

  /// Scene analysis for this timestamp.
  final SceneAnalysis? sceneAnalysis;

  /// Overall interest score based on all detections.
  final double interestScore;

  /// Additional metadata and analysis details.
  final Map<String, dynamic> metadata;

  VisionAnalysisResult({
    required this.timestamp,
    this.textDetections = const [],
    this.objectDetections = const [],
    this.faceDetections = const [],
    this.bodyPoseDetections = const [],
    this.handPoseDetections = const [],
    this.sceneAnalysis,
    required this.interestScore,
    this.metadata = const {},
  });

  /// Creates a [VisionAnalysisResult] from a map, typically from the platform channel.
  factory VisionAnalysisResult.fromMap(Map<dynamic, dynamic> map) {
    return VisionAnalysisResult(
      timestamp: (map['timestamp'] as num).toDouble(),
      textDetections: (map['textDetections'] as List<dynamic>? ?? [])
          .map((e) => TextDetection.fromMap(e))
          .toList(),
      objectDetections: (map['objectDetections'] as List<dynamic>? ?? [])
          .map((e) => ObjectDetection.fromMap(e))
          .toList(),
      faceDetections: (map['faceDetections'] as List<dynamic>? ?? [])
          .map((e) => FaceDetection.fromMap(e))
          .toList(),
      bodyPoseDetections: (map['bodyPoseDetections'] as List<dynamic>? ?? [])
          .map((e) => BodyPoseDetection.fromMap(e))
          .toList(),
      handPoseDetections: (map['handPoseDetections'] as List<dynamic>? ?? [])
          .map((e) => HandPoseDetection.fromMap(e))
          .toList(),
      sceneAnalysis: map['sceneAnalysis'] != null
          ? SceneAnalysis.fromMap(map['sceneAnalysis'])
          : null,
      interestScore: (map['interestScore'] as num?)?.toDouble() ?? 0.0,
      metadata: Map<String, dynamic>.from(map['metadata'] ?? {}),
    );
  }

  @override
  String toString() {
    return 'VisionAnalysisResult(timestamp: $timestamp, interestScore: $interestScore, '
        'objects: ${objectDetections.length}, text: ${textDetections.length}, faces: ${faceDetections.length}, '
        'poses: ${bodyPoseDetections.length}, hands: ${handPoseDetections.length})';
  }
}

/// Provides access to Apple's VisionKit functionalities.
class VisionKit {
  static const MethodChannel _channel = MethodChannel('visionkit');

  /// Detects text in the video at the given [path].
  ///
  /// Returns a list of [TextDetection] objects, each representing a piece of
  /// detected text, its confidence, timestamp, and bounding box.
  static Future<List<TextDetection>> detectTextInVideo(String path) async {
    try {
      debugPrint(
        "[VisionKitPlugin.dart] Invoking native method 'detectTextInVideo' with path: $path",
      );
      final List<dynamic>? results = await _channel.invokeMethod(
        'detectTextInVideo',
        {'videoPath': path},
      );

      if (results == null) {
        return [];
      }

      return results.map((e) => TextDetection.fromMap(e)).toList();
    } on PlatformException catch (e) {
      // Re-throw the exception to be handled by the UI layer.
      throw Exception("Failed to detect text: ${e.message}");
    }
  }

  /// Detects objects in the video at the given [path].
  ///
  /// Returns a list of [ObjectDetection] objects, each representing a detected
  /// object, its confidence, timestamp, and bounding box.
  static Future<List<ObjectDetection>> detectObjectsInVideo(String path) async {
    try {
      debugPrint(
        "[VisionKitPlugin.dart] Invoking native method 'detectObjectsInVideo' with path: $path",
      );
      final List<dynamic>? results = await _channel.invokeMethod(
        'detectObjectsInVideo',
        {'videoPath': path},
      );

      if (results == null) {
        return [];
      }

      return results.map((e) => ObjectDetection.fromMap(e)).toList();
    } on PlatformException catch (e) {
      // Re-throw the exception to be handled by the UI layer.
      throw Exception("Failed to detect objects: ${e.message}");
    }
  }

  /// Performs comprehensive analysis of the video including text, objects, and scene analysis.
  ///
  /// Returns a list of [VisionAnalysisResult] objects with combined analysis data
  /// and calculated interest scores for highlight detection.
  static Future<List<VisionAnalysisResult>> analyzeVideoComprehensively(
    String path, {
    Map<String, dynamic>? options,
  }) async {
    try {
      debugPrint(
        "[VisionKitPlugin.dart] Invoking native method 'analyzeVideoComprehensively' with path: $path",
      );
      final List<dynamic>? results = await _channel.invokeMethod(
        'analyzeVideoComprehensively',
        {'videoPath': path, 'options': options ?? {}},
      );

      if (results == null) {
        return [];
      }

      return results.map((e) => VisionAnalysisResult.fromMap(e)).toList();
    } on PlatformException catch (e) {
      // Re-throw the exception to be handled by the UI layer.
      throw Exception("Failed to analyze video comprehensively: ${e.message}");
    }
  }
}
