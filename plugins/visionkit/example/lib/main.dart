import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:visionkit/visionkit.dart';
import 'package:file_picker/file_picker.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String _status = 'Ready to analyze video';
  List<VisionAnalysisResult> _analysisResults = [];
  bool _isAnalyzing = false;

  @override
  void initState() {
    super.initState();
  }

  Future<void> _pickAndAnalyzeVideo() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final videoPath = result.files.single.path!;
        await _analyzeVideo(videoPath);
      }
    } catch (e) {
      setState(() {
        _status = 'Error picking file: $e';
      });
    }
  }

  Future<void> _analyzeVideo(String videoPath) async {
    setState(() {
      _isAnalyzing = true;
      _status = 'Analyzing video...';
      _analysisResults.clear();
    });

    try {
      // Perform comprehensive analysis
      final results = await VisionKit.analyzeVideoComprehensively(
        videoPath,
        options: {
          'enableTextDetection': true,
          'enableObjectDetection': true,
          'enableSceneAnalysis': true,
        },
      );

      setState(() {
        _analysisResults = results;
        _status = 'Analysis complete! Found ${results.length} analysis points.';
        _isAnalyzing = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Analysis failed: $e';
        _isAnalyzing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('VisionKit Enhanced Analysis'),
          backgroundColor: Colors.blue,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Text(
                        _status,
                        style: Theme.of(context).textTheme.titleMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton.icon(
                        onPressed: _isAnalyzing ? null : _pickAndAnalyzeVideo,
                        icon: _isAnalyzing
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.video_library),
                        label: Text(
                          _isAnalyzing
                              ? 'Analyzing...'
                              : 'Pick Video to Analyze',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              if (_analysisResults.isNotEmpty) ...[
                Text(
                  'Analysis Results',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: ListView.builder(
                    itemCount: _analysisResults.length,
                    itemBuilder: (context, index) {
                      final result = _analysisResults[index];
                      return _buildAnalysisResultCard(result);
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnalysisResultCard(VisionAnalysisResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Time: ${result.timestamp.toStringAsFixed(1)}s',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: _getScoreColor(result.interestScore),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Score: ${(result.interestScore * 100).toInt()}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (result.objectDetections.isNotEmpty) ...[
              Text(
                'Objects: ${result.objectDetections.map((o) => o.label).join(', ')}',
              ),
              const SizedBox(height: 4),
            ],
            if (result.textDetections.isNotEmpty) ...[
              Text(
                'Text: ${result.textDetections.map((t) => t.text).join(', ')}',
              ),
              const SizedBox(height: 4),
            ],
            if (result.sceneAnalysis != null) ...[
              Text('Scene: ${result.sceneAnalysis!.sceneType}'),
            ],
          ],
        ),
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score > 0.7) return Colors.green;
    if (score > 0.4) return Colors.orange;
    return Colors.red;
  }
}
