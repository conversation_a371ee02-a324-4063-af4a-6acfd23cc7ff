#!/bin/bash

# MediaPipe Model Download Script
# This script downloads all required MediaPipe models for the Flutter plugin

set -e  # Exit on any error

echo "🤖 Downloading MediaPipe Models..."
echo "=================================="

# Create directories
echo "📁 Creating asset directories..."
mkdir -p android/src/main/assets
mkdir -p ios/Assets

# Function to download and copy models
download_model() {
    local url=$1
    local filename=$2
    local description=$3
    
    echo "⬇️  Downloading $description..."
    echo "   URL: $url"
    echo "   File: $filename"
    
    # Download to Android assets first
    if curl -L --fail --show-error --progress-bar "$url" -o "android/src/main/assets/$filename"; then
        echo "✅ Downloaded $filename successfully"
        # Copy to iOS assets
        cp "android/src/main/assets/$filename" "ios/Assets/$filename"
        echo "📋 Copied to iOS assets"
    else
        echo "❌ Failed to download $filename"
        return 1
    fi
    echo ""
}

# Download pose landmarker (lite version for better performance)
download_model \
    "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task" \
    "pose_landmarker.task" \
    "Pose Landmarker (Lite)"

# Download hand landmarker
download_model \
    "https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task" \
    "hand_landmarker.task" \
    "Hand Landmarker"

# Download face landmarker
download_model \
    "https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task" \
    "face_landmarker.task" \
    "Face Landmarker"

# Download object detector (EfficientDet Lite0)
download_model \
    "https://storage.googleapis.com/mediapipe-models/object_detector/efficientdet_lite0/int8/1/efficientdet_lite0.tflite" \
    "efficientdet_lite0.tflite" \
    "Object Detector (EfficientDet Lite0)"

echo "🎉 All MediaPipe models downloaded successfully!"
echo ""
echo "📊 Model Summary:"
echo "=================="
echo "Android assets:"
ls -lh android/src/main/assets/ | grep -E '\.(task|tflite)$' || echo "No models found"
echo ""
echo "iOS assets:"
ls -lh ios/Assets/ | grep -E '\.(task|tflite)$' || echo "No models found"
echo ""
echo "✅ Setup complete! You can now use the MediaPipe plugin."
echo ""
echo "💡 Next steps:"
echo "   1. Run 'flutter pub get' in your main project"
echo "   2. Build and test your app"
echo "   3. Check the README.md for usage examples" 