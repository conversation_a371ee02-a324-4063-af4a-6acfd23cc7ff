# MediaPipe Plugin for Flutter

This plugin integrates Google's MediaPipe framework with Flutter, providing advanced pose detection, hand tracking, face mesh detection, and object detection capabilities.

## Features

- **Pose Detection**: Detect human body poses with 33 keypoints
- **Hand Tracking**: Track hand landmarks and gestures with handedness detection
- **Face Mesh**: Detect detailed facial landmarks and expressions
- **Object Detection**: Detect and classify objects using MobileNetV2
- **Real-time Progress**: Stream processing progress back to Flutter
- **Cross-platform**: Supports iOS and Android

## Setup

### 1. Add Plugin Dependency

Add the plugin to your `pubspec.yaml`:

```yaml
dependencies:
  mediapipe:
    path: ./plugins/mediapipe
```

### 2. Download MediaPipe Models

You need to download the MediaPipe model files and place them in the appropriate directories:

#### For Android (`android/src/main/assets/`):

- `pose_landmarker.task` - [Download from MediaPipe](https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task)
- `hand_landmarker.task` - [Download from MediaPipe](https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task)
- `face_landmarker.task` - [Download from MediaPipe](https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task)
- `efficientdet_lite0.tflite` - [Download from TensorFlow Hub](https://tfhub.dev/tensorflow/lite-model/efficientdet/lite0/detection/metadata/1?lite-format=tflite)

#### For iOS (`ios/Assets/`):

Place the same model files in the iOS assets directory.

### 3. Download Script

You can use this script to download all required models:

```bash
#!/bin/bash

# Create directories
mkdir -p android/src/main/assets
mkdir -p ios/Assets

# Download pose landmarker
curl -L "https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task" \
  -o android/src/main/assets/pose_landmarker.task
cp android/src/main/assets/pose_landmarker.task ios/Assets/

# Download hand landmarker
curl -L "https://storage.googleapis.com/mediapipe-models/hand_landmarker/hand_landmarker/float16/1/hand_landmarker.task" \
  -o android/src/main/assets/hand_landmarker.task
cp android/src/main/assets/hand_landmarker.task ios/Assets/

# Download face landmarker
curl -L "https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task" \
  -o android/src/main/assets/face_landmarker.task
cp android/src/main/assets/face_landmarker.task ios/Assets/

# Download object detector (EfficientDet Lite0)
curl -L "https://storage.googleapis.com/mediapipe-models/object_detector/efficientdet_lite0/int8/1/efficientdet_lite0.tflite" \
  -o android/src/main/assets/efficientdet_lite0.tflite
cp android/src/main/assets/efficientdet_lite0.tflite ios/Assets/

echo "All MediaPipe models downloaded successfully!"
```

### 4. iOS Configuration

Update your iOS `Info.plist` to include camera permissions if needed:

```xml
<key>NSCameraUsageDescription</key>
<string>This app uses the camera for video analysis</string>
```

### 5. Android Configuration

Make sure your `android/app/build.gradle` has the minimum SDK version:

```gradle
android {
    compileSdkVersion 34

    defaultConfig {
        minSdkVersion 21  // MediaPipe requires API 21+
        targetSdkVersion 34
    }
}
```

## Usage

### Basic Example

```dart
import 'package:mediapipe/mediapipe.dart';

// Analyze a video file
final results = await MediaPipe.analyzeVideo(
  '/path/to/video.mp4',
  enablePoseDetection: true,
  enableHandDetection: true,
  enableFaceDetection: true,
  enableObjectDetection: true,
  confidenceThreshold: 0.5,
  maxResults: 5,
);

// Process results
for (final result in results) {
  print('Timestamp: ${result.timestamp}');

  if (result.pose != null) {
    print('Pose detected with ${result.pose!.landmarks.length} landmarks');
  }

  if (result.hands.isNotEmpty) {
    for (final hand in result.hands) {
      print('${hand.handedness} hand detected');
    }
  }

  if (result.face != null) {
    print('Face detected with ${result.face!.landmarks.length} landmarks');
  }

  for (final object in result.objects) {
    print('Object: ${object.label} (${object.confidence})');
  }
}
```

### Real-time Processing

```dart
import 'package:mediapipe/mediapipe.dart';

final processor = MediaPipeProcessor();

// Initialize
await processor.initialize(
  enablePoseDetection: true,
  enableHandDetection: true,
  enableFaceDetection: true,
  enableObjectDetection: true,
);

// Listen to results
processor.resultsStream.listen((result) {
  // Handle real-time results
  print('Real-time result: $result');
});

// Process frame
await processor.processFrame(frameData, width, height);

// Dispose when done
await processor.dispose();
```

## Data Models

### MediaPipeResult

Contains all detection results for a single frame:

- `timestamp`: When the frame was processed
- `pose`: Pose detection data (nullable)
- `hands`: List of hand detections
- `face`: Face detection data (nullable)
- `objects`: List of object detections

### PoseData

Human pose detection with 33 landmarks:

- `landmarks`: List of 3D landmarks
- `confidence`: Detection confidence
- `timestamp`: Detection timestamp

### HandData

Hand detection and tracking:

- `landmarks`: 21 hand landmarks
- `confidence`: Detection confidence
- `handedness`: 'left' or 'right'
- `timestamp`: Detection timestamp

### FaceData

Face mesh detection:

- `landmarks`: 468 facial landmarks
- `confidence`: Detection confidence
- `boundingBox`: Face bounding box (optional)
- `timestamp`: Detection timestamp

### ObjectData

Object detection and classification:

- `label`: Object class name
- `confidence`: Detection confidence
- `boundingBox`: Object bounding box
- `timestamp`: Detection timestamp

## Performance Tips

1. **Model Selection**: Use lite models for better performance
2. **Confidence Threshold**: Higher thresholds reduce false positives
3. **Max Results**: Limit results to improve performance
4. **Frame Rate**: Process fewer frames for real-time applications
5. **Memory**: Dispose of processors when not needed

## Troubleshooting

### Model Loading Issues

- Ensure model files are in the correct directories
- Check file permissions and sizes
- Verify model file integrity

### Performance Issues

- Reduce confidence threshold
- Limit max results
- Use lite model variants
- Process fewer frames per second

### iOS Build Issues

- Check MediaPipe framework dependencies in Podfile
- Ensure minimum iOS version is 13.0+
- Verify Swift version compatibility

### Android Build Issues

- Check Gradle dependencies
- Ensure minimum SDK version is 21+
- Verify NDK compatibility

## License

This plugin integrates with Google's MediaPipe framework. Please refer to the [MediaPipe License](https://github.com/google/mediapipe/blob/master/LICENSE) for usage terms.

## Contributing

Contributions are welcome! Please read the contributing guidelines and submit pull requests for any improvements.
