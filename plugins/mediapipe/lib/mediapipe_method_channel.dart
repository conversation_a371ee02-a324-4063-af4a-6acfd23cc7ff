import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import 'mediapipe_platform_interface.dart';

/// An implementation of [MediapipePlatform] that uses method channels.
class MethodChannelMediapipe extends MediapipePlatform {
  /// The method channel used to interact with the native platform.
  @visibleForTesting
  final methodChannel = const MethodChannel('mediapipe');

  @override
  Future<String?> getPlatformVersion() async {
    final version = await methodChannel.invokeMethod<String>(
      'getPlatformVersion',
    );
    return version;
  }

  @override
  Future<List<Map<String, dynamic>>> analyzeVideo(
    String videoPath, {
    bool enablePoseDetection = true,
    bool enableHandDetection = true,
    bool enableFaceDetection = true,
    bool enableObjectDetection = true,
    double confidenceThreshold = 0.5,
    int maxResults = 5,
  }) async {
    final List<dynamic>? results = await methodChannel
        .invokeMethod('analyzeVideo', {
          'videoPath': videoPath,
          'enablePoseDetection': enablePoseDetection,
          'enableHandDetection': enableHandDetection,
          'enableFaceDetection': enableFaceDetection,
          'enableObjectDetection': enableObjectDetection,
          'confidenceThreshold': confidenceThreshold,
          'maxResults': maxResults,
        });

    return results?.cast<Map<String, dynamic>>() ?? [];
  }
}
