import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// A 3D point with x, y, z coordinates and visibility.
class Landmark {
  final double x;
  final double y;
  final double z;
  final double visibility;

  Landmark({
    required this.x,
    required this.y,
    required this.z,
    this.visibility = 1.0,
  });

  factory Landmark.fromMap(Map<dynamic, dynamic> map) {
    return Landmark(
      x: (map['x'] as num).toDouble(),
      y: (map['y'] as num).toDouble(),
      z: (map['z'] as num).toDouble(),
      visibility: (map['visibility'] as num?)?.toDouble() ?? 1.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {'x': x, 'y': y, 'z': z, 'visibility': visibility};
  }

  @override
  String toString() => 'Landmark(x: $x, y: $y, z: $z, visibility: $visibility)';
}

/// Pose detection data with 33 keypoints.
class PoseData {
  final List<Landmark> landmarks;
  final double confidence;
  final DateTime timestamp;

  PoseData({
    required this.landmarks,
    required this.confidence,
    required this.timestamp,
  });

  factory PoseData.fromMap(Map<dynamic, dynamic> map) {
    return PoseData(
      landmarks: (map['landmarks'] as List<dynamic>)
          .map((item) => Landmark.fromMap(item))
          .toList(),
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        (map['timestamp'] as num).toInt(),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'landmarks': landmarks.map((x) => x.toMap()).toList(),
      'confidence': confidence,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  @override
  String toString() =>
      'PoseData(landmarks: ${landmarks.length}, confidence: $confidence)';
}

/// Hand detection data with landmarks and handedness.
class HandData {
  final List<Landmark> landmarks;
  final double confidence;
  final String handedness; // 'left' or 'right'
  final DateTime timestamp;

  HandData({
    required this.landmarks,
    required this.confidence,
    required this.handedness,
    required this.timestamp,
  });

  factory HandData.fromMap(Map<dynamic, dynamic> map) {
    return HandData(
      landmarks: (map['landmarks'] as List<dynamic>)
          .map((item) => Landmark.fromMap(item))
          .toList(),
      confidence: (map['confidence'] as num).toDouble(),
      handedness: map['handedness'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        (map['timestamp'] as num).toInt(),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'landmarks': landmarks.map((x) => x.toMap()).toList(),
      'confidence': confidence,
      'handedness': handedness,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  @override
  String toString() =>
      'HandData(landmarks: ${landmarks.length}, handedness: $handedness, confidence: $confidence)';
}

/// Face detection data with mesh landmarks.
class FaceData {
  final List<Landmark> landmarks;
  final double confidence;
  final DateTime timestamp;
  final Rect? boundingBox;

  FaceData({
    required this.landmarks,
    required this.confidence,
    required this.timestamp,
    this.boundingBox,
  });

  factory FaceData.fromMap(Map<dynamic, dynamic> map) {
    Rect? boundingBox;
    if (map['boundingBox'] != null) {
      final boxMap = map['boundingBox'] as Map<dynamic, dynamic>;
      boundingBox = Rect.fromLTWH(
        (boxMap['x'] as num).toDouble(),
        (boxMap['y'] as num).toDouble(),
        (boxMap['width'] as num).toDouble(),
        (boxMap['height'] as num).toDouble(),
      );
    }

    return FaceData(
      landmarks: (map['landmarks'] as List<dynamic>)
          .map((item) => Landmark.fromMap(item))
          .toList(),
      confidence: (map['confidence'] as num).toDouble(),
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        (map['timestamp'] as num).toInt(),
      ),
      boundingBox: boundingBox,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'landmarks': landmarks.map((x) => x.toMap()).toList(),
      'confidence': confidence,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'boundingBox': boundingBox != null
          ? {
              'x': boundingBox!.left,
              'y': boundingBox!.top,
              'width': boundingBox!.width,
              'height': boundingBox!.height,
            }
          : null,
    };
  }

  @override
  String toString() =>
      'FaceData(landmarks: ${landmarks.length}, confidence: $confidence)';
}

/// Object detection data with classification and bounding box.
class ObjectData {
  final String label;
  final double confidence;
  final Rect boundingBox;
  final DateTime timestamp;

  ObjectData({
    required this.label,
    required this.confidence,
    required this.boundingBox,
    required this.timestamp,
  });

  factory ObjectData.fromMap(Map<dynamic, dynamic> map) {
    final boxMap = map['boundingBox'] as Map<dynamic, dynamic>;
    return ObjectData(
      label: map['label'] as String,
      confidence: (map['confidence'] as num).toDouble(),
      boundingBox: Rect.fromLTWH(
        (boxMap['x'] as num).toDouble(),
        (boxMap['y'] as num).toDouble(),
        (boxMap['width'] as num).toDouble(),
        (boxMap['height'] as num).toDouble(),
      ),
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        (map['timestamp'] as num).toInt(),
      ),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'confidence': confidence,
      'boundingBox': {
        'x': boundingBox.left,
        'y': boundingBox.top,
        'width': boundingBox.width,
        'height': boundingBox.height,
      },
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  @override
  String toString() => 'ObjectData(label: $label, confidence: $confidence)';
}

/// Combined MediaPipe result containing all detection data.
class MediaPipeResult {
  final DateTime timestamp;
  final PoseData? pose;
  final List<HandData> hands;
  final FaceData? face;
  final List<ObjectData> objects;

  MediaPipeResult({
    required this.timestamp,
    this.pose,
    this.hands = const [],
    this.face,
    this.objects = const [],
  });

  factory MediaPipeResult.fromMap(Map<dynamic, dynamic> map) {
    return MediaPipeResult(
      timestamp: DateTime.fromMillisecondsSinceEpoch(
        (map['timestamp'] as num).toInt(),
      ),
      pose: map['pose'] != null ? PoseData.fromMap(map['pose']) : null,
      hands: (map['hands'] as List<dynamic>? ?? [])
          .map((item) => HandData.fromMap(item))
          .toList(),
      face: map['face'] != null ? FaceData.fromMap(map['face']) : null,
      objects: (map['objects'] as List<dynamic>? ?? [])
          .map((item) => ObjectData.fromMap(item))
          .toList(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'timestamp': timestamp.millisecondsSinceEpoch,
      'pose': pose?.toMap(),
      'hands': hands.map((x) => x.toMap()).toList(),
      'face': face?.toMap(),
      'objects': objects.map((x) => x.toMap()).toList(),
    };
  }

  @override
  String toString() {
    return 'MediaPipeResult(timestamp: $timestamp, pose: ${pose != null}, hands: ${hands.length}, face: ${face != null}, objects: ${objects.length})';
  }
}

/// MediaPipe processor for video analysis.
class MediaPipeProcessor {
  static const MethodChannel _channel = MethodChannel('mediapipe');
  static const EventChannel _eventChannel = EventChannel('mediapipe/results');

  StreamSubscription? _subscription;
  StreamController<MediaPipeResult>? _resultsController;

  /// Initialize MediaPipe models and processors.
  Future<void> initialize({
    bool enablePoseDetection = true,
    bool enableHandDetection = true,
    bool enableFaceDetection = true,
    bool enableObjectDetection = true,
    double confidenceThreshold = 0.5,
    int maxResults = 5,
  }) async {
    try {
      await _channel.invokeMethod('initialize', {
        'enablePoseDetection': enablePoseDetection,
        'enableHandDetection': enableHandDetection,
        'enableFaceDetection': enableFaceDetection,
        'enableObjectDetection': enableObjectDetection,
        'confidenceThreshold': confidenceThreshold,
        'maxResults': maxResults,
      });
    } on PlatformException catch (e) {
      throw Exception('Failed to initialize MediaPipe: ${e.message}');
    }
  }

  /// Process a single frame from video.
  Future<void> processFrame(Uint8List frameData, int width, int height) async {
    try {
      await _channel.invokeMethod('processFrame', {
        'frameData': frameData,
        'width': width,
        'height': height,
      });
    } on PlatformException catch (e) {
      throw Exception('Failed to process frame: ${e.message}');
    }
  }

  /// Process an entire video file.
  Future<List<MediaPipeResult>> processVideo(String videoPath) async {
    try {
      final List<dynamic>? results = await _channel.invokeMethod(
        'processVideo',
        {'videoPath': videoPath},
      );

      if (results == null) {
        return [];
      }

      return results.map((e) => MediaPipeResult.fromMap(e)).toList();
    } on PlatformException catch (e) {
      throw Exception('Failed to process video: ${e.message}');
    }
  }

  /// Get stream of real-time results.
  Stream<MediaPipeResult> get resultsStream {
    _resultsController ??= StreamController<MediaPipeResult>.broadcast();

    _subscription ??= _eventChannel.receiveBroadcastStream().listen(
      (data) {
        if (data is Map) {
          final result = MediaPipeResult.fromMap(data);
          _resultsController?.add(result);
        }
      },
      onError: (error) {
        debugPrint('MediaPipe results stream error: $error');
      },
    );

    return _resultsController!.stream;
  }

  /// Dispose of resources.
  Future<void> dispose() async {
    await _subscription?.cancel();
    _subscription = null;
    await _resultsController?.close();
    _resultsController = null;

    try {
      await _channel.invokeMethod('dispose');
    } on PlatformException catch (e) {
      debugPrint('Failed to dispose MediaPipe: ${e.message}');
    }
  }
}

/// Main MediaPipe plugin class.
class MediaPipe {
  static const MethodChannel _channel = MethodChannel('mediapipe');

  /// Get the platform version.
  static Future<String?> getPlatformVersion() async {
    final version = await _channel.invokeMethod<String>('getPlatformVersion');
    return version;
  }

  /// Analyze video with MediaPipe.
  static Future<List<MediaPipeResult>> analyzeVideo(
    String videoPath, {
    bool enablePoseDetection = true,
    bool enableHandDetection = true,
    bool enableFaceDetection = true,
    bool enableObjectDetection = true,
    double confidenceThreshold = 0.5,
    int maxResults = 5,
  }) async {
    try {
      final List<dynamic>? results = await _channel
          .invokeMethod('analyzeVideo', {
            'videoPath': videoPath,
            'enablePoseDetection': enablePoseDetection,
            'enableHandDetection': enableHandDetection,
            'enableFaceDetection': enableFaceDetection,
            'enableObjectDetection': enableObjectDetection,
            'confidenceThreshold': confidenceThreshold,
            'maxResults': maxResults,
          });

      if (results == null) {
        return [];
      }

      return results.map((e) => MediaPipeResult.fromMap(e)).toList();
    } on PlatformException catch (e) {
      throw Exception('Failed to analyze video: ${e.message}');
    }
  }
}
