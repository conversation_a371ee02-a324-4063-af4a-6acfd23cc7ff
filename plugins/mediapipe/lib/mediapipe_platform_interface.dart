import 'package:plugin_platform_interface/plugin_platform_interface.dart';

import 'mediapipe_method_channel.dart';

abstract class MediapipePlatform extends PlatformInterface {
  /// Constructs a MediapipePlatform.
  MediapipePlatform() : super(token: _token);

  static final Object _token = Object();

  static MediapipePlatform _instance = MethodChannelMediapipe();

  /// The default instance of [MediapipePlatform] to use.
  ///
  /// Defaults to [MethodChannelMediapipe].
  static MediapipePlatform get instance => _instance;

  /// Platform-specific implementations should set this with their own
  /// platform-specific class that extends [MediapipePlatform] when
  /// they register themselves.
  static set instance(MediapipePlatform instance) {
    PlatformInterface.verifyToken(instance, _token);
    _instance = instance;
  }

  Future<String?> getPlatformVersion() {
    throw UnimplementedError('getPlatformVersion() has not been implemented.');
  }

  Future<List<Map<String, dynamic>>> analyzeVideo(
    String videoPath, {
    bool enablePoseDetection = true,
    bool enableHandDetection = true,
    bool enableFaceDetection = true,
    bool enableObjectDetection = true,
    double confidenceThreshold = 0.5,
    int maxResults = 5,
  }) {
    throw UnimplementedError('analyzeVideo() has not been implemented.');
  }
}
