import 'package:flutter_test/flutter_test.dart';
import 'package:mediapipe/mediapipe_platform_interface.dart';
import 'package:mediapipe/mediapipe_method_channel.dart';
import 'package:plugin_platform_interface/plugin_platform_interface.dart';

class MockMediapipePlatform
    with MockPlatformInterfaceMixin
    implements MediapipePlatform {
  @override
  Future<String?> getPlatformVersion() => Future.value('42');

  @override
  Future<List<Map<String, dynamic>>> analyzeVideo(
    String videoPath, {
    bool enablePoseDetection = false,
    bool enableHandDetection = false,
    bool enableFaceDetection = false,
    bool enableObjectDetection = false,
    double confidenceThreshold = 0.5,
    int maxResults = 5,
  }) => Future.value([]);
}

void main() {
  final MediapipePlatform initialPlatform = MediapipePlatform.instance;

  test('$MethodChannelMediapipe is the default instance', () {
    expect(initialPlatform, isInstanceOf<MethodChannelMediapipe>());
  });

  test('getPlatformVersion', () async {
    MockMediapipePlatform fakePlatform = MockMediapipePlatform();
    MediapipePlatform.instance = fakePlatform;

    expect(await fakePlatform.getPlatformVersion(), '42');
  });
}
