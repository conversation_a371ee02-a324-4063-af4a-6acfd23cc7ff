group = "com.example.mediapipe"
version = "1.0-SNAPSHOT"

buildscript {
    ext.kotlin_version = "1.7.10"
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath("com.android.tools.build:gradle:7.3.0")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

apply plugin: "com.android.library"
apply plugin: "kotlin-android"

android {
    if (project.android.hasProperty("namespace")) {
        namespace = "com.example.mediapipe"
    }

    compileSdkVersion = 34

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"
    }

    sourceSets {
        main.java.srcDirs += "src/main/kotlin"
        test.java.srcDirs += "src/test/kotlin"
    }

    defaultConfig {
        minSdkVersion = 21
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    dependencies {
        implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
        
        // MediaPipe dependencies
        implementation 'com.google.mediapipe:tasks-vision:0.10.0'
        implementation 'com.google.mediapipe:tasks-common:0.10.0'
        
        // Required for video processing
        implementation 'androidx.camera:camera-core:1.3.0'
        implementation 'androidx.camera:camera-camera2:1.3.0'
        implementation 'androidx.camera:camera-lifecycle:1.3.0'
        implementation 'androidx.camera:camera-video:1.3.0'
        
        testImplementation 'junit:junit:4.13.2'
        androidTestImplementation 'androidx.test:runner:1.5.2'
        androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    }

    testOptions {
        unitTests.all {
            useJUnitPlatform()

            testLogging {
               events "passed", "skipped", "failed", "standardOut", "standardError"
               outputs.upToDateWhen {false}
               showStandardStreams = true
            }
        }
    }
}
