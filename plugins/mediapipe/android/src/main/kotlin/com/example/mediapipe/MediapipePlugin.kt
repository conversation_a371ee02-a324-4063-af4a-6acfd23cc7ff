package com.example.mediapipe

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.media.MediaMetadataRetriever
import android.os.Handler
import android.os.Looper
import android.util.Log
import androidx.annotation.NonNull
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.tasks.common.BaseOptions
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.facedetector.FaceDetector
import com.google.mediapipe.tasks.vision.facedetector.FaceDetectorOptions
import com.google.mediapipe.tasks.vision.facelandmarker.FaceLandmarker
import com.google.mediapipe.tasks.vision.facelandmarker.FaceLandmarkerOptions
import com.google.mediapipe.tasks.vision.handlandmarker.HandLandmarker
import com.google.mediapipe.tasks.vision.handlandmarker.HandLandmarkerOptions
import com.google.mediapipe.tasks.vision.objectdetector.ObjectDetector
import com.google.mediapipe.tasks.vision.objectdetector.ObjectDetectorOptions
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarkerOptions
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.EventChannel
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList

/** MediapipePlugin */
class MediapipePlugin :
    FlutterPlugin,
    MethodCallHandler,
    EventChannel.StreamHandler {
    // The MethodChannel that will the communication between Flutter and native Android
    //
    // This local reference serves to register the plugin with the Flutter Engine and unregister it
    // when the Flutter Engine is detached from the Activity
    private lateinit var channel: MethodChannel
    private lateinit var eventChannel: EventChannel
    private lateinit var context: Context
    private var eventSink: EventChannel.EventSink? = null
    
    // MediaPipe detectors
    private var poseLandmarker: PoseLandmarker? = null
    private var handLandmarker: HandLandmarker? = null
    private var faceLandmarker: FaceLandmarker? = null
    private var objectDetector: ObjectDetector? = null
    
    // Configuration
    private var confidenceThreshold: Float = 0.5f
    private var maxResults: Int = 5
    
    private val mainHandler = Handler(Looper.getMainLooper())
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    companion object {
        private const val TAG = "MediapipePlugin"
        private const val CHANNEL_NAME = "mediapipe"
        private const val EVENT_CHANNEL_NAME = "mediapipe/results"
    }

    override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, CHANNEL_NAME)
        channel.setMethodCallHandler(this)
        
        eventChannel = EventChannel(flutterPluginBinding.binaryMessenger, EVENT_CHANNEL_NAME)
        eventChannel.setStreamHandler(this)
        
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
        when (call.method) {
            "getPlatformVersion" -> {
            result.success("Android ${android.os.Build.VERSION.RELEASE}")
            }
            "initialize" -> {
                handleInitialize(call, result)
            }
            "analyzeVideo" -> {
                handleAnalyzeVideo(call, result)
            }
            "dispose" -> {
                handleDispose(result)
            }
            else -> {
            result.notImplemented()
            }
        }
    }
    
    override fun onListen(arguments: Any?, events: EventChannel.EventSink?) {
        eventSink = events
    }
    
    override fun onCancel(arguments: Any?) {
        eventSink = null
    }

    private fun handleInitialize(call: MethodCall, result: Result) {
        val arguments = call.arguments as? Map<String, Any> ?: run {
            result.error("INVALID_ARGUMENTS", "Invalid arguments", null)
            return
        }
        
        val enablePoseDetection = arguments["enablePoseDetection"] as? Boolean ?: true
        val enableHandDetection = arguments["enableHandDetection"] as? Boolean ?: true
        val enableFaceDetection = arguments["enableFaceDetection"] as? Boolean ?: true
        val enableObjectDetection = arguments["enableObjectDetection"] as? Boolean ?: true
        
        confidenceThreshold = (arguments["confidenceThreshold"] as? Double)?.toFloat() ?: 0.5f
        maxResults = arguments["maxResults"] as? Int ?: 5
        
        Log.d(TAG, "Initializing with pose:$enablePoseDetection, hand:$enableHandDetection, face:$enableFaceDetection, object:$enableObjectDetection")
        
        coroutineScope.launch {
            try {
                // Initialize pose detector
                if (enablePoseDetection) {
                    val poseOptions = PoseLandmarkerOptions.builder()
                        .setBaseOptions(BaseOptions.builder().setModelAssetPath("pose_landmarker.task").build())
                        .setRunningMode(RunningMode.VIDEO)
                        .setMinPoseDetectionConfidence(confidenceThreshold)
                        .setMinPosePresenceConfidence(confidenceThreshold)
                        .setMinTrackingConfidence(confidenceThreshold)
                        .setNumPoses(maxResults)
                        .build()
                    
                    poseLandmarker = PoseLandmarker.createFromOptions(context, poseOptions)
                    Log.d(TAG, "Pose detector initialized")
                }
                
                // Initialize hand detector
                if (enableHandDetection) {
                    val handOptions = HandLandmarkerOptions.builder()
                        .setBaseOptions(BaseOptions.builder().setModelAssetPath("hand_landmarker.task").build())
                        .setRunningMode(RunningMode.VIDEO)
                        .setMinHandDetectionConfidence(confidenceThreshold)
                        .setMinHandPresenceConfidence(confidenceThreshold)
                        .setMinTrackingConfidence(confidenceThreshold)
                        .setNumHands(maxResults)
                        .build()
                    
                    handLandmarker = HandLandmarker.createFromOptions(context, handOptions)
                    Log.d(TAG, "Hand detector initialized")
                }
                
                // Initialize face detector
                if (enableFaceDetection) {
                    val faceOptions = FaceLandmarkerOptions.builder()
                        .setBaseOptions(BaseOptions.builder().setModelAssetPath("face_landmarker.task").build())
                        .setRunningMode(RunningMode.VIDEO)
                        .setMinFaceDetectionConfidence(confidenceThreshold)
                        .setMinFacePresenceConfidence(confidenceThreshold)
                        .setMinTrackingConfidence(confidenceThreshold)
                        .setNumFaces(maxResults)
                        .build()
                    
                    faceLandmarker = FaceLandmarker.createFromOptions(context, faceOptions)
                    Log.d(TAG, "Face detector initialized")
                }
                
                // Initialize object detector
                if (enableObjectDetection) {
                    val objectOptions = ObjectDetectorOptions.builder()
                        .setBaseOptions(BaseOptions.builder().setModelAssetPath("efficientdet_lite0.tflite").build())
                        .setRunningMode(RunningMode.VIDEO)
                        .setScoreThreshold(confidenceThreshold)
                        .setMaxResults(maxResults)
                        .build()
                    
                    objectDetector = ObjectDetector.createFromOptions(context, objectOptions)
                    Log.d(TAG, "Object detector initialized")
                }
                
                withContext(Dispatchers.Main) {
                    result.success(null)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Initialization error", e)
                withContext(Dispatchers.Main) {
                    result.error("INITIALIZATION_FAILED", "Failed to initialize MediaPipe: ${e.message}", null)
                }
            }
        }
    }
    
    private fun handleAnalyzeVideo(call: MethodCall, result: Result) {
        val arguments = call.arguments as? Map<String, Any> ?: run {
            result.error("INVALID_ARGUMENTS", "Invalid arguments", null)
            return
        }
        
        val videoPath = arguments["videoPath"] as? String ?: run {
            result.error("INVALID_ARGUMENTS", "Video path is required", null)
            return
        }
        
        Log.d(TAG, "Analyzing video: $videoPath")
        
        coroutineScope.launch {
            try {
                val results = analyzeVideoSync(videoPath)
                withContext(Dispatchers.Main) {
                    result.success(results)
                }
            } catch (e: Exception) {
                Log.e(TAG, "Analysis error", e)
                withContext(Dispatchers.Main) {
                    result.error("ANALYSIS_FAILED", "Failed to analyze video: ${e.message}", null)
                }
            }
        }
    }
    
    private suspend fun analyzeVideoSync(videoPath: String): List<Map<String, Any>> {
        val file = File(videoPath)
        if (!file.exists()) {
            throw Exception("Video file does not exist: $videoPath")
        }
        
        val retriever = MediaMetadataRetriever()
        retriever.setDataSource(videoPath)
        
        val durationStr = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION)
        val durationMs = durationStr?.toLongOrNull() ?: 0L
        val durationSeconds = durationMs / 1000.0
        
        Log.d(TAG, "Video duration: ${durationSeconds}s")
        
        // Analyze frames at 1 second intervals
        val frameInterval = 1000000L // 1 second in microseconds
        val totalFrames = (durationMs * 1000 / frameInterval).toInt()
        
        val allResults = ArrayList<Map<String, Any>>()
        
        for (i in 0 until totalFrames) {
            val timeUs = i * frameInterval
            if (timeUs >= durationMs * 1000) break
            
            try {
                val bitmap = retriever.getFrameAtTime(timeUs, MediaMetadataRetriever.OPTION_CLOSEST_SYNC)
                if (bitmap != null) {
                    val mpImage = BitmapImageBuilder(bitmap).build()
                    val frameResults = analyzeFrame(mpImage, timeUs / 1000) // Convert to milliseconds
                    allResults.addAll(frameResults)
                    
                    // Progress reporting
                    val progress = (i + 1).toDouble() / totalFrames
                    mainHandler.post {
                        eventSink?.success(mapOf(
                            "progress" to progress,
                            "statusMessage" to "Analyzing frame ${i + 1} of $totalFrames..."
                        ))
                    }
                    
                    // Add small delay to prevent overwhelming the system
                    Thread.sleep(50)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Error processing frame at ${timeUs}us: ${e.message}")
                continue
            }
        }
        
        retriever.release()
        Log.d(TAG, "Analysis complete. Found ${allResults.size} results.")
        return allResults
    }
    
    private fun analyzeFrame(image: MPImage, timestampMs: Long): List<Map<String, Any>> {
        val results = ArrayList<Map<String, Any>>()
        
        // Pose detection
        poseLandmarker?.let { detector ->
            try {
                val poseResult = detector.detectForVideo(image, timestampMs)
                
                for ((index, landmarks) in poseResult.landmarks().withIndex()) {
                    val confidence = 0.8 // Approximate confidence
                    
                    val poseData = mapOf(
                        "timestamp" to timestampMs,
                        "pose" to mapOf(
                            "landmarks" to landmarks.map { landmark ->
                                mapOf(
                                    "x" to landmark.x(),
                                    "y" to landmark.y(),
                                    "z" to landmark.z(),
                                    "visibility" to (landmark.visibility().orElse(1.0f))
                                )
                            },
                            "confidence" to confidence,
                            "timestamp" to timestampMs
                        )
                    )
                    
                    results.add(poseData)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Pose detection error: ${e.message}")
            }
        }
        
        // Hand detection
        handLandmarker?.let { detector ->
            try {
                val handResult = detector.detectForVideo(image, timestampMs)
                
                for ((index, landmarks) in handResult.landmarks().withIndex()) {
                    val handedness = if (index < handResult.handedness().size) {
                        handResult.handedness()[index].firstOrNull()?.categoryName()?.lowercase() ?: "unknown"
                    } else "unknown"
                    
                    val confidence = if (index < handResult.handedness().size) {
                        handResult.handedness()[index].firstOrNull()?.score() ?: 0.5f
                    } else 0.5f
                    
                    val handData = mapOf(
                        "timestamp" to timestampMs,
                        "hands" to listOf(mapOf(
                            "landmarks" to landmarks.map { landmark ->
                                mapOf(
                                    "x" to landmark.x(),
                                    "y" to landmark.y(),
                                    "z" to landmark.z(),
                                    "visibility" to 1.0
                                )
                            },
                            "confidence" to confidence,
                            "handedness" to handedness,
                            "timestamp" to timestampMs
                        ))
                    )
                    
                    results.add(handData)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Hand detection error: ${e.message}")
            }
        }
        
        // Face detection
        faceLandmarker?.let { detector ->
            try {
                val faceResult = detector.detectForVideo(image, timestampMs)
                
                for (landmarks in faceResult.landmarks()) {
                    val faceData = mapOf(
                        "timestamp" to timestampMs,
                        "face" to mapOf(
                            "landmarks" to landmarks.map { landmark ->
                                mapOf(
                                    "x" to landmark.x(),
                                    "y" to landmark.y(),
                                    "z" to landmark.z(),
                                    "visibility" to (landmark.visibility().orElse(1.0f))
                                )
                            },
                            "confidence" to 0.8, // Approximate confidence
                            "timestamp" to timestampMs
                        )
                    )
                    
                    results.add(faceData)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Face detection error: ${e.message}")
            }
        }
        
        // Object detection
        objectDetector?.let { detector ->
            try {
                val objectResult = detector.detectForVideo(image, timestampMs)
                
                for (detection in objectResult.detections()) {
                    val category = detection.categories().firstOrNull() ?: continue
                    
                    val objectData = mapOf(
                        "timestamp" to timestampMs,
                        "objects" to listOf(mapOf(
                            "label" to category.categoryName(),
                            "confidence" to category.score(),
                            "boundingBox" to mapOf(
                                "x" to detection.boundingBox().left,
                                "y" to detection.boundingBox().top,
                                "width" to detection.boundingBox().width(),
                                "height" to detection.boundingBox().height()
                            ),
                            "timestamp" to timestampMs
                        ))
                    )
                    
                    results.add(objectData)
                }
            } catch (e: Exception) {
                Log.w(TAG, "Object detection error: ${e.message}")
            }
        }
        
        return results
    }
    
    private fun handleDispose(result: Result) {
        poseLandmarker?.close()
        handLandmarker?.close()
        faceLandmarker?.close()
        objectDetector?.close()
        
        poseLandmarker = null
        handLandmarker = null
        faceLandmarker = null
        objectDetector = null
        
        Log.d(TAG, "Disposed all detectors")
        result.success(null)
    }

    override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        eventChannel.setStreamHandler(null)
    }
}
