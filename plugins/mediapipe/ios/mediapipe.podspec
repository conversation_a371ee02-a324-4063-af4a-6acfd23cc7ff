#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint mediapipe.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'mediapipe'
  s.version          = '0.0.1'
  s.summary          = 'MediaPipe integration for Flutter - pose, hand, face, and object detection'
  s.description      = <<-DESC
MediaPipe integration for Flutter providing pose detection, hand tracking, face mesh, and object detection capabilities using Google's MediaPipe framework.
                       DESC
  s.homepage         = 'https://github.com/8ight/aiclipper'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '13.0'

  # Add MediaPipe dependencies
  s.dependency 'MediaPipeTasksVision', '0.10.14'
  s.dependency 'MediaPipeTasksCommon', '0.10.14'
  
  # Add required frameworks
  s.framework = 'AVFoundation'
  s.framework = 'CoreMedia'
  s.framework = 'CoreVideo'
  s.framework = 'Accelerate'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'

  # Add MediaPipe model assets
  s.resource_bundles = {'mediapipe_assets' => ['Assets/*']}
  
  # If your plugin requires a privacy manifest, for example if it uses any
  # required reason APIs, update the PrivacyInfo.xcprivacy file to describe your
  # plugin's privacy impact, and then uncomment this line. For more information,
  # see https://developer.apple.com/documentation/bundleresources/privacy_manifest_files
  # s.resource_bundles = {'mediapipe_privacy' => ['Resources/PrivacyInfo.xcprivacy']}
end
