import Flutter
import UIKit
import AVFoundation
import MediaPipeTasksVision

@available(iOS 13.0, *)
public class MediapipePlugin: NSObject, FlutterPlugin, FlutterStreamHandler {
    private var eventSink: FlutterEventSink?
    
    // MediaPipe detectors
    private var poseDetector: PoseLandmarker?
    private var handDetector: HandLandmarker?
    private var faceDetector: FaceLandmarker?
    private var objectDetector: ObjectDetector?
    
    // Configuration
    private var confidenceThreshold: Float = 0.5
    private var maxResults: Int = 5
    
    private func timestamp() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter.string(from: Date())
    }
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: "mediapipe", binaryMessenger: registrar.messenger())
        let instance = MediapipePlugin()
        registrar.addMethodCallDelegate(instance, channel: channel)
        
        let eventChannel = FlutterEventChannel(name: "mediapipe/results", binaryMessenger: registrar.messenger())
        eventChannel.setStreamHandler(instance)
    }
    
    public func onListen(withArguments arguments: Any?, eventSink events: @escaping FlutterEventSink) -> FlutterError? {
        self.eventSink = events
        return nil
    }
    
    public func onCancel(withArguments arguments: Any?) -> FlutterError? {
        self.eventSink = nil
        return nil
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        switch call.method {
        case "getPlatformVersion":
            result("iOS " + UIDevice.current.systemVersion)
        case "initialize":
            handleInitialize(call: call, result: result)
        case "analyzeVideo":
            handleAnalyzeVideo(call: call, result: result)
        case "dispose":
            handleDispose(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func handleInitialize(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any] else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        let enablePoseDetection = args["enablePoseDetection"] as? Bool ?? true
        let enableHandDetection = args["enableHandDetection"] as? Bool ?? true
        let enableFaceDetection = args["enableFaceDetection"] as? Bool ?? true
        let enableObjectDetection = args["enableObjectDetection"] as? Bool ?? true
        
        confidenceThreshold = args["confidenceThreshold"] as? Float ?? 0.5
        maxResults = args["maxResults"] as? Int ?? 5
        
        print("\(timestamp()) [MediaPipePlugin] Initializing with pose:\(enablePoseDetection), hand:\(enableHandDetection), face:\(enableFaceDetection), object:\(enableObjectDetection)")
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                // Initialize pose detector
                if enablePoseDetection {
                    let poseOptions = PoseLandmarkerOptions()
                    poseOptions.runningMode = .video
                    poseOptions.minPoseDetectionConfidence = self.confidenceThreshold
                    poseOptions.minPosePresenceConfidence = self.confidenceThreshold
                    poseOptions.minTrackingConfidence = self.confidenceThreshold
                    poseOptions.numPoses = self.maxResults
                    
                    self.poseDetector = try PoseLandmarker(options: poseOptions)
                    print("\(self.timestamp()) [MediaPipePlugin] Pose detector initialized")
                }
                
                // Initialize hand detector
                if enableHandDetection {
                    let handOptions = HandLandmarkerOptions()
                    handOptions.runningMode = .video
                    handOptions.minHandDetectionConfidence = self.confidenceThreshold
                    handOptions.minHandPresenceConfidence = self.confidenceThreshold
                    handOptions.minTrackingConfidence = self.confidenceThreshold
                    handOptions.numHands = self.maxResults
                    
                    self.handDetector = try HandLandmarker(options: handOptions)
                    print("\(self.timestamp()) [MediaPipePlugin] Hand detector initialized")
                }
                
                // Initialize face detector
                if enableFaceDetection {
                    let faceOptions = FaceLandmarkerOptions()
                    faceOptions.runningMode = .video
                    faceOptions.minFaceDetectionConfidence = self.confidenceThreshold
                    faceOptions.minFacePresenceConfidence = self.confidenceThreshold
                    faceOptions.minTrackingConfidence = self.confidenceThreshold
                    faceOptions.numFaces = self.maxResults
                    
                    self.faceDetector = try FaceLandmarker(options: faceOptions)
                    print("\(self.timestamp()) [MediaPipePlugin] Face detector initialized")
                }
                
                // Initialize object detector
                if enableObjectDetection {
                    let objectOptions = ObjectDetectorOptions()
                    objectOptions.runningMode = .video
                    objectOptions.scoreThreshold = self.confidenceThreshold
                    objectOptions.maxResults = self.maxResults
                    
                    self.objectDetector = try ObjectDetector(options: objectOptions)
                    print("\(self.timestamp()) [MediaPipePlugin] Object detector initialized")
                }
                
                DispatchQueue.main.async {
                    result(nil)
                }
            } catch {
                print("\(self.timestamp()) [MediaPipePlugin] Initialization error: \(error)")
                DispatchQueue.main.async {
                    result(FlutterError(code: "INITIALIZATION_FAILED", message: "Failed to initialize MediaPipe: \(error.localizedDescription)", details: nil))
                }
            }
        }
    }
    
    private func handleAnalyzeVideo(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any],
              let videoPath = args["videoPath"] as? String else {
            result(FlutterError(code: "INVALID_ARGUMENTS", message: "Invalid arguments", details: nil))
            return
        }
        
        print("\(timestamp()) [MediaPipePlugin] Analyzing video: \(videoPath)")
        
        DispatchQueue.global(qos: .userInitiated).async {
            do {
                let results = try self.analyzeVideoSync(videoPath: videoPath)
                DispatchQueue.main.async {
                    result(results)
                }
            } catch {
                print("\(self.timestamp()) [MediaPipePlugin] Analysis error: \(error)")
                DispatchQueue.main.async {
                    result(FlutterError(code: "ANALYSIS_FAILED", message: "Failed to analyze video: \(error.localizedDescription)", details: nil))
                }
            }
        }
    }
    
    private func analyzeVideoSync(videoPath: String) throws -> [[String: Any]] {
        let videoURL = URL(fileURLWithPath: videoPath)
        let asset = AVAsset(url: videoURL)
        
        guard asset.isPlayable else {
            throw NSError(domain: "MediaPipePlugin", code: 1, userInfo: [NSLocalizedDescriptionKey: "Asset is not playable"])
        }
        
        guard let videoTrack = asset.tracks(withMediaType: .video).first else {
            throw NSError(domain: "MediaPipePlugin", code: 2, userInfo: [NSLocalizedDescriptionKey: "No video tracks found"])
        }
        
        let imageGenerator = AVAssetImageGenerator(asset: asset)
        imageGenerator.appliesPreferredTrackTransform = true
        imageGenerator.requestedTimeToleranceBefore = CMTime.zero
        imageGenerator.requestedTimeToleranceAfter = CMTime.zero
        
        let duration = asset.duration
        let durationInSeconds = CMTimeGetSeconds(duration)
        
        print("\(timestamp()) [MediaPipePlugin] Video duration: \(durationInSeconds)s")
        
        // Analyze frames at 1 second intervals
        let frameInterval: Double = 1.0
        let totalFrames = Int(ceil(durationInSeconds / frameInterval))
        
        var allResults: [[String: Any]] = []
        
        for i in 0..<totalFrames {
            let timeInSeconds = Double(i) * frameInterval
            let time = CMTime(seconds: timeInSeconds, preferredTimescale: 600)
            
            guard CMTimeCompare(time, asset.duration) < 0 else { break }
            
            do {
                let cgImage = try imageGenerator.copyCGImage(at: time, actualTime: nil)
                let uiImage = UIImage(cgImage: cgImage)
                let mpImage = try MPImage(uiImage: uiImage)
                
                let frameResults = try analyzeFrame(image: mpImage, timestamp: time)
                if !frameResults.isEmpty {
                    allResults.append(contentsOf: frameResults)
                }
                
                // Progress reporting
                let progress = Double(i + 1) / Double(totalFrames)
                if let sink = self.eventSink {
                    DispatchQueue.main.async {
                        sink([
                            "progress": progress,
                            "statusMessage": "Analyzing frame \(i + 1) of \(totalFrames)..."
                        ])
                    }
                }
                
                // Add small delay to prevent overwhelming the system
                usleep(50000) // 50ms delay
                
            } catch {
                print("\(timestamp()) [MediaPipePlugin] Error processing frame at \(timeInSeconds)s: \(error)")
                continue
            }
        }
        
        print("\(timestamp()) [MediaPipePlugin] Analysis complete. Found \(allResults.count) results.")
        return allResults
    }
    
    private func analyzeFrame(image: MPImage, timestamp: CMTime) throws -> [[String: Any]] {
        var results: [[String: Any]] = []
        let timestampMs = Int(CMTimeGetSeconds(timestamp) * 1000)
        
        // Pose detection
        if let poseDetector = self.poseDetector {
            let poseResult = try poseDetector.detect(videoFrame: image, timestampInMilliseconds: timestampMs)
            
            for (index, landmarks) in poseResult.landmarks.enumerated() {
                let confidence = index < poseResult.landmarks.count ? 0.8 : 0.5 // Approximate confidence
                
                let poseData: [String: Any] = [
                    "timestamp": timestampMs,
                    "pose": [
                        "landmarks": landmarks.map { landmark in
                            [
                                "x": landmark.x,
                                "y": landmark.y,
                                "z": landmark.z,
                                "visibility": landmark.visibility?.floatValue ?? 1.0
                            ]
                        },
                        "confidence": confidence,
                        "timestamp": timestampMs
                    ]
                ]
                
                results.append(poseData)
            }
        }
        
        // Hand detection
        if let handDetector = self.handDetector {
            let handResult = try handDetector.detect(videoFrame: image, timestampInMilliseconds: timestampMs)
            
            for (index, landmarks) in handResult.landmarks.enumerated() {
                let handedness = index < handResult.handedness.count ? 
                    handResult.handedness[index].first?.categoryName ?? "unknown" : "unknown"
                let confidence = index < handResult.handedness.count ? 
                    handResult.handedness[index].first?.score ?? 0.5 : 0.5
                
                let handData: [String: Any] = [
                    "timestamp": timestampMs,
                    "hands": [[
                        "landmarks": landmarks.map { landmark in
                            [
                                "x": landmark.x,
                                "y": landmark.y,
                                "z": landmark.z,
                                "visibility": 1.0
                            ]
                        },
                        "confidence": confidence,
                        "handedness": handedness.lowercased(),
                        "timestamp": timestampMs
                    ]]
                ]
                
                results.append(handData)
            }
        }
        
        // Face detection
        if let faceDetector = self.faceDetector {
            let faceResult = try faceDetector.detect(videoFrame: image, timestampInMilliseconds: timestampMs)
            
            for landmarks in faceResult.faceLandmarks {
                let faceData: [String: Any] = [
                    "timestamp": timestampMs,
                    "face": [
                        "landmarks": landmarks.map { landmark in
                            [
                                "x": landmark.x,
                                "y": landmark.y,
                                "z": landmark.z,
                                "visibility": landmark.visibility?.floatValue ?? 1.0
                            ]
                        },
                        "confidence": 0.8, // Approximate confidence
                        "timestamp": timestampMs
                    ]
                ]
                
                results.append(faceData)
            }
        }
        
        // Object detection
        if let objectDetector = self.objectDetector {
            let objectResult = try objectDetector.detect(videoFrame: image, timestampInMilliseconds: timestampMs)
            
            for detection in objectResult.detections {
                guard let category = detection.categories.first else { continue }
                
                let objectData: [String: Any] = [
                    "timestamp": timestampMs,
                    "objects": [[
                        "label": category.categoryName,
                        "confidence": category.score,
                        "boundingBox": [
                            "x": detection.boundingBox.origin.x,
                            "y": detection.boundingBox.origin.y,
                            "width": detection.boundingBox.size.width,
                            "height": detection.boundingBox.size.height
                        ],
                        "timestamp": timestampMs
                    ]]
                ]
                
                results.append(objectData)
            }
        }
        
        return results
    }
    
    private func handleDispose(result: @escaping FlutterResult) {
        poseDetector = nil
        handDetector = nil
        faceDetector = nil
        objectDetector = nil
        
        print("\(timestamp()) [MediaPipePlugin] Disposed all detectors")
        result(nil)
    }
}
