# Plan: Clean VisionKit Integration

The goal is to add Apple's VisionKit capabilities as a completely separate and independent feature, without touching the existing `whisper` or `opencv` plugins.

## Phase 1: Create a Dedicated `visionkit` Plugin

A new, standalone Flutter plugin will be created specifically for VisionKit.

### 1.1. Plugin Scaffolding

- **Command:** `flutter create --template=plugin --platforms=ios visionkit` inside the `plugins/` directory.
- This creates the standard structure:
  ```
  plugins/
  └── visionkit/
      ├── ios/                  <-- Native iOS code lives here
      ├── lib/                  <-- Dart API lives here
      └── pubspec.yaml
  ```

### 1.2. Why a separate plugin?

- **Cleanliness:** It ensures VisionKit code is never mixed with OpenCV.
- **Independence:** The `visionkit` plugin can be developed, tested, and updated without affecting any other part of the app.
- **Clarity:** It makes the project structure logical and easy to understand. `opencv` for OpenCV, `visionkit` for VisionKit.

## Phase 2: Implement the Native iOS Side (Swift)

Inside the new `visionkit` plugin, the Swift code will be written to interact with Apple's frameworks.

### 2.1. File

`plugins/visionkit/ios/Classes/VisionKitPlugin.swift`

### 2.2. Actions

- `import Vision`: To access computer vision algorithms like text and object recognition.
- `import AVFoundation`: To extract frames from the video file provided by the Flutter app.
- Create native functions that take a video path, extract frames, and run them through the Vision framework.
- These functions will handle requests from the Dart side and return the results (e.g., a list of detected text and its location).

## Phase 3: Implement the Dart API

A clean Dart API will be created within the plugin to make it easy to call from the main app.

### 3.1. File

`plugins/visionkit/lib/visionkit.dart`

### 3.2. Actions

- Create a `VisionKit` class with static methods.
- Define simple, clear methods like: `Future<List<TextDetection>> detectTextInVideo(String path)`
- This API will be the only thing the main application needs to interact with.

## Phase 4: Integrate into the Flutter App

Once the plugin is built, it will be integrated into the main application UI.

### 4.1. `pubspec.yaml`

- Add the new local plugin:
  ```yaml
  dependencies:
    # ... other dependencies
    visionkit:
      path: ./plugins/visionkit
  ```

### 4.2. New `VisionKitTab`

- Create a new `lib/widgets/visionkit_tab.dart` file.
- This new widget will `import 'package:visionkit/visionkit.dart';` and use the clean API from Phase 3.

### 4.3. Update `main.dart`

- Add the `VisionKitTab` to the `TabBar` to create the final 3-tab layout.

## Final Architecture Diagram

```mermaid
graph TD;
    subgraph "Main Flutter App"
        A[AI Clipper UI] --> B[Whisper Tab];
        A --> C[OpenCV Tab];
        A --> D["VisionKit Tab (New)"];
    end

    subgraph "Flutter Plugins (plugins/)"
        P1["aiclipper_whisper"];
        P2["opencv"];
        P3["visionkit (New)"];
    end

    subgraph "Native iOS Frameworks"
        F1[Speech];
        F2[AVFoundation];
        F3[OpenCV.xcframework];
        F4[Vision.framework];
    end

    B --> P1;
    C --> P2;
    D --> P3;

    P1 --> F1;
    P1 --> F2;
    P2 --> F3;
    P3 --> F4;
    P3 --> F2;

    style A fill:#8E74E4,stroke:#333,stroke-width:2px,color:#fff
    style B fill:#5C6BC0,stroke:#333,stroke-width:2px,color:#fff
    style C fill:#42A5F5,stroke:#333,stroke-width:2px,color:#fff
    style D fill:#66BB6A,stroke:#333,stroke-width:2px,color:#fff

    style P1 fill:#5C6BC0,stroke:#333,stroke-width:2px,color:#fff
    style P2 fill:#42A5F5,stroke:#333,stroke-width:2px,color:#fff
    style P3 fill:#66BB6A,stroke:#333,stroke-width:2px,color:#fff

    style F1 fill:#5C6BC0,stroke:#333,stroke-width:1px,color:#fff
    style F2 fill:#7E57C2,stroke:#333,stroke-width:1px,color:#fff
    style F3 fill:#42A5F5,stroke:#333,stroke-width:1px,color:#fff
    style F4 fill:#66BB6A,stroke:#333,stroke-width:1px,color:#fff
```
