# OpenCV Real Implementation Testing Guide

## Overview

This guide helps you test the real OpenCV computer vision implementation that replaces AVFoundation simulation with actual OpenCV algorithms.

## What We've Implemented

### 1. **Real OpenCV C++ Bridge Functions**

- `analyze_video_comprehensive()` - Full video analysis with scene detection
- `extract_meaningful_segments()` - Intelligent segment extraction
- `analyze_motion_opencv()` - Optical flow-based motion analysis
- `assess_quality_opencv()` - Multi-metric quality assessment
- `detect_faces_opencv()` - Face detection with Haar cascades + HOG
- `analyze_visual_content_opencv()` - Content classification and analysis
- `get_video_info_opencv()` - Complete video metadata

### 2. **Computer Vision Algorithms Used**

- **Scene Change Detection**: Histogram correlation analysis
- **Motion Analysis**: Lucas-Kanade optical flow tracking
- **Quality Assessment**: Laplacian variance, brightness, contrast, colorfulness
- **Face Detection**: HOG people detector + Haar cascades
- **Content Analysis**: Edge density, text region detection, color clustering
- **Composition Analysis**: Rule of thirds, visual balance scoring

## Testing on iOS Device

### Prerequisites

1. **Real iOS device** (OpenCV won't work in simulator)
2. **Xcode project** configured with opencv2.framework
3. **Test video files** in device storage

### Step 1: Build and Install

```bash
# Clean and rebuild with new OpenCV implementation
cd ios
rm -rf Pods Podfile.lock
pod install
cd ..

# Build for device (not simulator)
flutter build ios --release
# or
flutter run --release -d YOUR_DEVICE_ID
```

### Step 2: Test Basic Initialization

```dart
// In your test widget or main app
import 'package:flutter/services.dart';

class OpenCVTester extends StatefulWidget {
  @override
  _OpenCVTesterState createState() => _OpenCVTesterState();
}

class _OpenCVTesterState extends State<OpenCVTester> {
  static const platform = MethodChannel('opencv');
  String _status = 'Not tested';
  String _version = 'Unknown';

  Future<void> testOpenCVInit() async {
    try {
      // Test initialization
      final success = await platform.invokeMethod('initialize');
      if (success) {
        final version = await platform.invokeMethod('getVersion');
        setState(() {
          _status = 'OpenCV initialized successfully';
          _version = version;
        });
      } else {
        setState(() {
          _status = 'OpenCV initialization failed';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('OpenCV Testing')),
      body: Column(
        children: [
          Text('Status: $_status'),
          Text('Version: $_version'),
          ElevatedButton(
            onPressed: testOpenCVInit,
            child: Text('Test OpenCV Init'),
          ),
          ElevatedButton(
            onPressed: testVideoAnalysis,
            child: Text('Test Video Analysis'),
          ),
        ],
      ),
    );
  }

  Future<void> testVideoAnalysis() async {
    try {
      // You'll need to add a test video to your iOS bundle
      final result = await platform.invokeMethod('getVideoInfo', {
        'videoPath': '/path/to/test/video.mp4'
      });

      setState(() {
        _status = 'Video info: $result';
      });
    } catch (e) {
      setState(() {
        _status = 'Video analysis error: $e';
      });
    }
  }
}
```

### Step 3: Test Individual Functions

```dart
// Test motion analysis
Future<void> testMotionAnalysis(String videoPath) async {
  final result = await platform.invokeMethod('analyzeMotion', {
    'videoPath': videoPath,
    'startTime': 0.0,
    'duration': 10.0,
  });
  print('Motion analysis: $result');
}

// Test quality assessment
Future<void> testQualityAssessment(String videoPath) async {
  final result = await platform.invokeMethod('assessQuality', {
    'videoPath': videoPath,
    'startTime': 0.0,
    'duration': 5.0,
  });
  print('Quality assessment: $result');
}

// Test face detection
Future<void> testFaceDetection(String videoPath) async {
  final result = await platform.invokeMethod('detectFaces', {
    'videoPath': videoPath,
    'startTime': 0.0,
    'duration': 5.0,
  });
  print('Face detection: $result');
}

// Test meaningful segment extraction
Future<void> testMeaningfulSegments(String videoPath) async {
  final result = await platform.invokeMethod('extractMeaningfulSegments', {
    'videoPath': videoPath,
    'minDuration': 5.0,
    'maxDuration': 30.0,
    'qualityThreshold': 0.7,
    'maxSegments': 10,
  });
  print('Meaningful segments: $result');
}
```

## Expected Outputs

### Video Info Response

```json
{
  "duration": 120.5,
  "width": 1920,
  "height": 1080,
  "fps": 30.0,
  "frameCount": 3615,
  "codec": "detected",
  "bitrate": 5242880.0
}
```

### Motion Analysis Response

```json
{
  "averageIntensity": 0.65,
  "maxIntensity": 0.89,
  "variance": 0.12,
  "dominantDirection": "horizontal",
  "cameraMotion": "pan",
  "activityLevel": "high"
}
```

### Quality Assessment Response

```json
{
  "sharpness": 0.78,
  "brightness": 0.65,
  "contrast": 0.72,
  "colorfulness": 0.81,
  "overallScore": 0.74,
  "noiseLevel": 0.15,
  "exposureScore": 0.85
}
```

### Meaningful Segments Response

```json
[
  {
    "timestamp": 25.3,
    "duration": 12.7,
    "compositeScore": 0.85,
    "extractionReason": "high_engagement",
    "motionIntensity": 0.72,
    "qualityScore": 0.89,
    "engagementScore": 0.91,
    "faceCount": 2,
    "contentType": "interview",
    "audioCompatibility": 0.8,
    "textCompatibility": 0.3
  }
]
```

## Troubleshooting

### Common Issues

1. **OpenCV headers not found during build**

   - Ensure opencv2.framework is in `ios/Frameworks/`
   - Check podspec has correct framework path
   - Clean and rebuild: `flutter clean && flutter pub get`

2. **Crash on real device**

   ```
   // Add to iOS Info.plist if needed
   <key>NSCameraUsageDescription</key>
   <string>App needs camera for video analysis</string>
   ```

3. **Memory issues with large videos**

   - Videos are processed in segments for memory efficiency
   - Sample frames are analyzed, not every frame
   - Consider reducing analysis duration for testing

4. **Face detection not working**
   - HOG detector is primary, Haar cascade is fallback
   - Works better with frontal faces
   - Requires sufficient face size (>20x20 pixels)

### Performance Expectations

| Video Resolution | Processing Time | Memory Usage |
| ---------------- | --------------- | ------------ |
| 720p (5 min)     | 30-60 seconds   | ~200MB       |
| 1080p (5 min)    | 60-120 seconds  | ~400MB       |
| 4K (5 min)       | 2-5 minutes     | ~800MB       |

## Integration with Whisper & VisionKit

### Workflow

1. **OpenCV Analysis** → Extract meaningful segments
2. **Whisper Processing** → Transcribe audio from high-scoring segments
3. **VisionKit Processing** → Extract text from segments with text regions
4. **Cross-Modal Fusion** → Combine visual, audio, and text insights

### Example Integration

```dart
Future<Map<String, dynamic>> processVideoComprehensively(String videoPath) async {
  // Step 1: OpenCV analysis
  final segments = await testMeaningfulSegments(videoPath);

  final results = <Map<String, dynamic>>[];

  for (final segment in segments) {
    final segmentResult = <String, dynamic>{
      'segment': segment,
    };

    // Step 2: Process with Whisper if audio-optimized
    if (segment['audioCompatibility'] > 0.6) {
      // Extract audio segment and process with Whisper
      // segmentResult['transcript'] = await whisperAnalysis(...)
    }

    // Step 3: Process with VisionKit if text-optimized
    if (segment['textCompatibility'] > 0.6) {
      // Extract frames and process with VisionKit
      // segmentResult['textRecognition'] = await visionKitAnalysis(...)
    }

    results.add(segmentResult);
  }

  return {
    'segments': results,
    'overallScore': segments.isEmpty ? 0.0 :
        segments.map((s) => s['compositeScore']).reduce((a, b) => a + b) / segments.length,
  };
}
```

## Next Steps

1. **Test on real iOS device** with the methods above
2. **Validate OpenCV algorithms** are working as expected
3. **Measure performance** with different video types
4. **Integrate with Whisper** for audio processing
5. **Add VisionKit** for text recognition
6. **Optimize parameters** based on your specific use cases

## Debug Logging

Add this to your iOS implementation for debugging:

```swift
// In OpencvPlugin.swift
private func debugLog(_ message: String) {
    #if DEBUG
    print("[OpenCV Debug] \(message)")
    #endif
}
```

This real OpenCV implementation provides the foundation for intelligent video analysis that can guide both Whisper and VisionKit processing for maximum efficiency and accuracy.
