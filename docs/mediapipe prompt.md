# 🎮 AI Video Clipper – MediaPipe + MobileNetV2 Integration Prompt

This is a full integration prompt for adding **MediaPipe** (including MobileNetV2-based object detection) to an existing Flutter-based AI video clipper that already uses:

- ✅ **OpenCV** for frame processing and overlays
- ✅ **VisionKit** for scene interest analysis
- ✅ **Whisper** for speech transcription

This prompt guides implementation across iOS and Android targets, with performance and scalability in mind.

---

## 🌟 Objective

Enable advanced on-device visual analysis using MediaPipe, including:

- Pose detection (33 keypoints)
- Hand landmark tracking
- Face mesh detection
- Object detection / classification via **MobileNetV2 (I320)**

MediaPipe will return structured per-frame metadata that can be combined with Whisper and VisionKit for smarter highlight scoring and scene segmentation.

---

## ✅ Dart API Interface

```dart
class MediaPipeResult {
  final DateTime timestamp;
  final PoseData? pose;
  final HandData? hands;
  final FaceData? face;
  final List<ObjectData> objects;
}

class MediaPipeProcessor {
  Future<void> initialize();
  void processFrame(InputImage frame);
  Stream<MediaPipeResult> get resultsStream;
  Future<void> dispose();
}
```

---

## ⚡ Integration Example (Flutter)

```dart
final mp = MediaPipeProcessor();
await mp.initialize();

cameraStream.listen(mp.processFrame);

mp.resultsStream.listen((mpResult) {
  clipScorer.addFrame(
    ClipFrameData(
      timestamp: mpResult.timestamp,
      interestScore: visionKitData.interestScore,
      transcription: whisperData.textAt(mpResult.timestamp),
      poseConfidence: mpResult.pose?.confidence ?? 0,
      faceConfidence: mpResult.face?.confidence ?? 0,
      objectLabels: mpResult.objects.map((o) => o.label).toList(),
    ),
  );
});
```

---

## 🛠️ Setup Steps (Cross-platform)

### 1. Add Native MediaPipe Dependencies

- iOS: Integrate MediaPipe Vision Tasks via CocoaPods or Swift Package
- Android: Include `mediapipe-tasks-vision` dependency in Gradle

### 2. Configure MediaPipe Pipelines (Android Example)

```kotlin
val options = ObjectDetector.ObjectDetectorOptions.builder()
  .setBaseOptions(
    BaseOptions.builder().setModelAssetPath("mobilenet_v2_i320.tflite").build()
  )
  .setRunningMode(RunningMode.VIDEO)
  .setMaxResults(5)
  .setScoreThreshold(0.5f)
  .build()

val objectDetector = ObjectDetector.createFromOptions(context, options)
```

### 3. Add Pose, Hands, and Face Processors

Use `PoseLandmarker`, `HandLandmarker`, and `FaceLandmarker` from MediaPipe Tasks Vision.

### 4. Bridge to Flutter

- Use `MethodChannel` or `flutter_rust_bridge` to stream results back to Dart
- Ensure processing occurs in a native thread, not the UI thread

---

## 🤖 Output Format (Example JSON)

```json
{
  "timestamp": 4.0,
  "interestScore": 0.95,
  "transcription": "Let's go",
  "pose": { "confidence": 0.87 },
  "hands": { "count": 2 },
  "face": { "visible": true, "confidence": 0.92 },
  "object": ["dog", "ball"]
}
```

---

## 📊 Threading and Performance

- Run MediaPipe models on native background thread
- Only send structured results to Dart layer
- Use `MOBILENET_V2_I320` for good quality/speed balance
- Allow configuration of resolution or max frame rate

---

## 🔄 Integration Flow

```
Camera / Video Feed
       ⬇
[ Native MediaPipe Pipelines ]
       ⬇
   MediaPipeResult
       ⬇
 Combine with Whisper + VisionKit + OpenCV
       ⬇
     Clip Scoring Engine
       ⬇
     Output Highlights
```

---

## ✅ Summary

| Framework | Role                                      |
| --------- | ----------------------------------------- |
| OpenCV    | Frame overlays, motion detection          |
| VisionKit | Scene scoring (`interestScore`, momentum) |
| Whisper   | Speech-to-text (timeline aligned)         |
| MediaPipe | Pose, face, hand, object detection        |

---

## 🚀 Next Steps

- [ ] Implement `MediaPipeProcessor` and native wrappers
- [ ] Stream results into Dart layer and merge
- [ ] Profile memory, adjust resolution if needed
- [ ] Tune clip scoring thresholds using all signal inputs

---

Need native code scaffolding or a sample plugin? Just ask.
