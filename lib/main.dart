import 'package:flutter/material.dart';
import 'widgets/whisper_tab.dart';
import 'widgets/opencv_tab.dart';
import 'widgets/visionkit_tab.dart';
import 'widgets/mediapipe_tab.dart';
import 'widgets/combined_analysis_example.dart';
// import 'widgets/mediapipe_tab.dart';
// import 'widgets/combined_analysis_example.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'AiClipper - AI Video Analysis',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const HomePage(),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AiClipper - AI Video Analysis'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.voice_chat),
              text: 'Whisper AI\n(Speech Recognition)',
            ),
            Tab(
              icon: Icon(Icons.video_camera_back),
              text: 'OpenCV\n(Computer Vision)',
            ),
            Tab(
              icon: Icon(Icons.visibility),
              text: 'VisionKit\n(Apple Vision)',
            ),
            Tab(
              icon: Icon(Icons.gesture),
              text: 'MediaPipe\n(Google ML)',
            ),
            Tab(
              icon: Icon(Icons.integration_instructions),
              text: 'Combined\n(All-in-One)',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          WhisperTab(),
          OpenCVTab(),
          VisionKitTab(),
          MediaPipeTab(),
          CombinedAnalysisExample(),
        ],
      ),
    );
  }
}
