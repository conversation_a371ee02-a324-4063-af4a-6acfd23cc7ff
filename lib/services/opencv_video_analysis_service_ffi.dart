import 'package:flutter/foundation.dart';
import 'package:opencv_ffi/opencv_ffi.dart';

/// Simplified video analysis service using OpenCV FFI
class OpenCVVideoAnalysisService {
  static bool _initialized = false;

  /// Initialize the OpenCV service
  static Future<bool> initialize() async {
    if (_initialized) return true;

    try {
      _initialized = await OpenCV.initialize();
      if (_initialized) {
        debugPrint(
            'OpenCV FFI initialized successfully: ${OpenCV.getVersion()}');
      }
      return _initialized;
    } catch (e) {
      debugPrint('OpenCV FFI initialization failed: $e');
      return false;
    }
  }

  /// Get OpenCV version
  static String getVersion() {
    return OpenCV.getVersion();
  }

  /// Analyze motion in video segment
  static Future<double> analyzeMotion(String videoPath,
      {double startTime = 0.0, double duration = 5.0}) async {
    await _ensureInitialized();
    return OpenCV.analyzeMotion(videoPath, startTime, duration);
  }

  /// Assess video quality
  static Future<double> assessQuality(String videoPath) async {
    await _ensureInitialized();
    return OpenCV.assessQuality(videoPath);
  }

  /// Detect faces in video segment
  static Future<int> detectFaces(String videoPath,
      {double startTime = 0.0, double duration = 5.0}) async {
    await _ensureInitialized();
    return OpenCV.detectFaces(videoPath, startTime, duration);
  }

  /// Analyze entire video and return highlights
  static Future<List<Map<String, dynamic>>> analyzeVideo(
      String videoPath) async {
    await _ensureInitialized();
    return OpenCV.analyzeVideo(videoPath);
  }

  /// Analyze motion with detailed metrics
  static Future<Map<String, dynamic>?> analyzeMotionDetailed(String videoPath,
      {double startTime = 0.0, double duration = 5.0}) async {
    await _ensureInitialized();
    return OpenCV.analyzeMotionDetailed(videoPath, startTime, duration);
  }

  /// Assess quality with detailed metrics
  static Future<Map<String, dynamic>?> assessQualityDetailed(
      String videoPath) async {
    await _ensureInitialized();
    return OpenCV.assessQualityDetailed(videoPath);
  }

  /// Detect faces with detailed metrics
  static Future<Map<String, dynamic>?> detectFacesDetailed(String videoPath,
      {double startTime = 0.0, double duration = 5.0}) async {
    await _ensureInitialized();
    return OpenCV.detectFacesDetailed(videoPath, startTime, duration);
  }

  /// Comprehensive video analysis
  static Future<Map<String, dynamic>> performComprehensiveAnalysis(
    String videoPath, {
    bool enableMotionDetection = true,
    bool enableQualityAssessment = true,
    bool enableFaceDetection = true,
  }) async {
    await _ensureInitialized();

    final results = <String, dynamic>{};

    try {
      if (enableMotionDetection) {
        results['motion'] = await analyzeMotion(videoPath);
      }

      if (enableQualityAssessment) {
        results['quality'] = await assessQuality(videoPath);
      }

      if (enableFaceDetection) {
        results['faces'] = await detectFaces(videoPath);
      }

      // Get video highlights
      results['highlights'] = await analyzeVideo(videoPath);

      return results;
    } catch (e) {
      debugPrint('Comprehensive analysis failed: $e');
      return {'error': e.toString()};
    }
  }

  /// Comprehensive video analysis with detailed metrics
  static Future<Map<String, dynamic>> performDetailedAnalysis(
    String videoPath, {
    bool enableMotionDetection = true,
    bool enableQualityAssessment = true,
    bool enableFaceDetection = true,
    double startTime = 0.0,
    double duration = 5.0,
  }) async {
    await _ensureInitialized();

    final results = <String, dynamic>{};

    try {
      if (enableMotionDetection) {
        results['motion_basic'] = await analyzeMotion(videoPath,
            startTime: startTime, duration: duration);
        results['motion_detailed'] = await analyzeMotionDetailed(videoPath,
            startTime: startTime, duration: duration);
      }

      if (enableQualityAssessment) {
        results['quality_basic'] = await assessQuality(videoPath);
        results['quality_detailed'] = await assessQualityDetailed(videoPath);
      }

      if (enableFaceDetection) {
        results['faces_basic'] = await detectFaces(videoPath,
            startTime: startTime, duration: duration);
        results['faces_detailed'] = await detectFacesDetailed(videoPath,
            startTime: startTime, duration: duration);
      }

      // Get video highlights
      results['highlights'] = await analyzeVideo(videoPath);

      return results;
    } catch (e) {
      debugPrint('Detailed analysis failed: $e');
      return {'error': e.toString()};
    }
  }

  /// Ensure OpenCV is initialized
  static Future<void> _ensureInitialized() async {
    if (!_initialized) {
      final success = await initialize();
      if (!success) {
        throw Exception('OpenCV not initialized');
      }
    }
  }
}
