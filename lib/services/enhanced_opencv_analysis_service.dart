import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'opencv_video_analysis_service_ffi.dart';

/// Enhanced video analysis service with comprehensive scene action detection
class EnhancedOpenCVAnalysisService {
  static bool _initialized = false;

  /// Initialize the enhanced analysis service
  static Future<bool> initialize() async {
    if (_initialized) {
      return true;
    }

    try {
      _initialized = await OpenCVVideoAnalysisService.initialize();
      if (_initialized) {
        debugPrint('Enhanced OpenCV Analysis Service initialized successfully');
      }
      return _initialized;
    } catch (e) {
      debugPrint('Enhanced OpenCV Analysis Service initialization failed: $e');
      return false;
    }
  }

  /// Comprehensive video analysis with all available metrics
  static Future<VideoAnalysisResult> performComprehensiveVideoAnalysis(
    String videoPath, {
    double analysisInterval = 5.0, // Analyze every 5 seconds (reduced from 2.0)
    double segmentDuration =
        3.0, // Analyze 3-second segments (reduced from 5.0)
    bool enableSceneDetection = true,
    bool enableMotionAnalysis = true,
    bool enableQualityAssessment = true,
    bool enableFaceDetection = true,
    bool enableActivityAnalysis = true,
    Function(double)? progressCallback, // Add progress callback
  }) async {
    await _ensureInitialized();

    try {
      final File videoFile = File(videoPath);
      if (!await videoFile.exists()) {
        throw Exception('Video file not found: $videoPath');
      }

      // Get actual video duration with a more conservative estimate
      final videoDuration = await _getActualVideoDuration(videoPath);

      // Cap analysis to prevent excessive processing
      final maxAnalysisDuration =
          math.min(videoDuration, 600.0); // Max 10 minutes
      final estimatedSegments = (maxAnalysisDuration / analysisInterval).ceil();

      debugPrint(
          'Analyzing video: duration=${videoDuration}s, segments=$estimatedSegments');

      final List<VideoSegmentAnalysis> segmentAnalyses = [];
      final List<SceneChangePoint> sceneChanges = [];
      final List<ActionMoment> actionMoments = [];
      final List<QualityMoment> qualityMoments = [];

      int processedSegments = 0;

      // Analyze video in segments with periodic yielding
      for (double timestamp = 0;
          timestamp < maxAnalysisDuration;
          timestamp += analysisInterval) {
        final actualDuration =
            math.min<double>(segmentDuration, maxAnalysisDuration - timestamp);

        final segmentAnalysis = await _analyzeVideoSegment(
          videoPath,
          timestamp,
          actualDuration,
          enableMotionAnalysis: enableMotionAnalysis,
          enableQualityAssessment: enableQualityAssessment,
          enableFaceDetection: enableFaceDetection,
        );

        segmentAnalyses.add(segmentAnalysis);
        processedSegments++;

        // Update progress
        final progress = processedSegments / estimatedSegments;
        progressCallback?.call(progress);

        // Yield to UI thread every few segments to prevent freezing
        if (processedSegments % 3 == 0) {
          await Future.delayed(Duration(milliseconds: 10));
        }
      }

      debugPrint('Processed $processedSegments segments');

      // Detect scene changes using motion and quality analysis
      if (enableSceneDetection) {
        sceneChanges.addAll(await _detectSceneChanges(segmentAnalyses));
        // Yield after scene detection
        await Future.delayed(Duration(milliseconds: 5));
      }

      // Identify action moments
      if (enableActivityAnalysis) {
        actionMoments.addAll(await _identifyActionMoments(segmentAnalyses));
        // Yield after action analysis
        await Future.delayed(Duration(milliseconds: 5));
      }

      // Identify quality moments
      qualityMoments.addAll(await _identifyQualityMoments(segmentAnalyses));
      await Future.delayed(Duration(milliseconds: 5));

      // Generate intelligent clip suggestions
      final clipSuggestions = await _generateClipSuggestions(
        segmentAnalyses,
        sceneChanges,
        actionMoments,
        qualityMoments,
      );

      return VideoAnalysisResult(
        videoPath: videoPath,
        duration: maxAnalysisDuration,
        segmentAnalyses: segmentAnalyses,
        sceneChanges: sceneChanges,
        actionMoments: actionMoments,
        qualityMoments: qualityMoments,
        clipSuggestions: clipSuggestions,
        overallMetrics: _calculateOverallMetrics(segmentAnalyses),
      );
    } catch (e) {
      debugPrint('Comprehensive video analysis failed: $e');
      throw Exception('Video analysis failed: $e');
    }
  }

  /// Extract intelligent clips based on comprehensive analysis
  static Future<List<ClipSuggestion>> extractIntelligentClips(
    String videoPath, {
    double minClipDuration = 3.0,
    double maxClipDuration = 30.0,
    int maxClips = 10,
    double confidenceThreshold = 0.6,
  }) async {
    final analysis = await performComprehensiveVideoAnalysis(videoPath);

    return analysis.clipSuggestions
        .where((clip) =>
            clip.duration >= minClipDuration &&
            clip.duration <= maxClipDuration &&
            clip.confidence >= confidenceThreshold)
        .take(maxClips)
        .toList();
  }

  /// Analyze a specific video segment with detailed metrics
  static Future<VideoSegmentAnalysis> _analyzeVideoSegment(
    String videoPath,
    double startTime,
    double duration, {
    bool enableMotionAnalysis = true,
    bool enableQualityAssessment = true,
    bool enableFaceDetection = true,
  }) async {
    final Map<String, dynamic> analysisResults = {};

    // Basic motion analysis
    if (enableMotionAnalysis) {
      try {
        final motionScore = await OpenCVVideoAnalysisService.analyzeMotion(
          videoPath,
          startTime: startTime,
          duration: duration,
        );
        analysisResults['motion_score'] = motionScore;

        // Enhanced motion analysis if available
        final detailedMotion =
            await OpenCVVideoAnalysisService.analyzeMotionDetailed(
          videoPath,
          startTime: startTime,
          duration: duration,
        );
        analysisResults['motion_detailed'] = detailedMotion;
      } catch (e) {
        debugPrint('Motion analysis failed for segment $startTime: $e');
        analysisResults['motion_score'] = 0.0;
      }
    }

    // Quality assessment
    if (enableQualityAssessment) {
      try {
        final qualityScore =
            await OpenCVVideoAnalysisService.assessQuality(videoPath);
        analysisResults['quality_score'] = qualityScore;

        final detailedQuality =
            await OpenCVVideoAnalysisService.assessQualityDetailed(videoPath);
        analysisResults['quality_detailed'] = detailedQuality;
      } catch (e) {
        debugPrint('Quality assessment failed for segment $startTime: $e');
        analysisResults['quality_score'] = 0.0;
      }
    }

    // Face detection
    if (enableFaceDetection) {
      try {
        final faceCount = await OpenCVVideoAnalysisService.detectFaces(
          videoPath,
          startTime: startTime,
          duration: duration,
        );
        analysisResults['face_count'] = faceCount;

        final detailedFaces =
            await OpenCVVideoAnalysisService.detectFacesDetailed(
          videoPath,
          startTime: startTime,
          duration: duration,
        );
        analysisResults['faces_detailed'] = detailedFaces;
      } catch (e) {
        debugPrint('Face detection failed for segment $startTime: $e');
        analysisResults['face_count'] = 0;
      }
    }

    // Calculate composite scores
    final motionScore = analysisResults['motion_score'] ?? 0.0;
    final qualityScore = analysisResults['quality_score'] ?? 0.0;
    final faceCount = analysisResults['face_count'] ?? 0;

    // Enhanced scoring algorithm
    final activityLevel = _calculateActivityLevel(motionScore, faceCount);
    final visualInterest =
        _calculateVisualInterest(qualityScore, motionScore, faceCount);
    final clipWorthiness = _calculateClipWorthiness(analysisResults);

    return VideoSegmentAnalysis(
      startTime: startTime,
      duration: duration,
      motionScore: motionScore,
      qualityScore: qualityScore,
      faceCount: faceCount,
      activityLevel: activityLevel,
      visualInterest: visualInterest,
      clipWorthiness: clipWorthiness,
      rawResults: analysisResults,
    );
  }

  /// Detect scene changes based on analysis data
  static Future<List<SceneChangePoint>> _detectSceneChanges(
    List<VideoSegmentAnalysis> segments,
  ) async {
    final List<SceneChangePoint> sceneChanges = [];

    for (int i = 1; i < segments.length; i++) {
      final prev = segments[i - 1];
      final curr = segments[i];

      // Calculate differences between consecutive segments
      final motionDiff = (curr.motionScore - prev.motionScore).abs();
      final qualityDiff = (curr.qualityScore - prev.qualityScore).abs();
      final faceDiff = (curr.faceCount - prev.faceCount).abs();

      // Scene change detection algorithm
      final motionChangeScore = motionDiff > 0.3 ? motionDiff : 0.0;
      final qualityChangeScore = qualityDiff > 0.2 ? qualityDiff : 0.0;
      final faceChangeScore = faceDiff > 1 ? faceDiff / 5.0 : 0.0;

      final overallChangeScore = (motionChangeScore * 0.5 +
          qualityChangeScore * 0.3 +
          faceChangeScore * 0.2);

      if (overallChangeScore > 0.4) {
        sceneChanges.add(SceneChangePoint(
          timestamp: curr.startTime,
          changeScore: overallChangeScore,
          changeType: _determineChangeType(motionDiff, qualityDiff, faceDiff),
          confidence: math.min(overallChangeScore, 1.0),
        ));
      }
    }

    return sceneChanges;
  }

  /// Identify action moments with high activity
  static Future<List<ActionMoment>> _identifyActionMoments(
    List<VideoSegmentAnalysis> segments,
  ) async {
    final List<ActionMoment> actionMoments = [];

    // Calculate motion percentiles for relative comparison
    final motionScores = segments.map((s) => s.motionScore).toList();
    motionScores.sort();
    final motionP75 = motionScores[(motionScores.length * 0.75).floor()];
    final motionP90 = motionScores[(motionScores.length * 0.90).floor()];

    for (final segment in segments) {
      String actionType = 'low';
      double actionIntensity = segment.activityLevel;

      if (segment.motionScore > motionP90) {
        actionType = 'high';
        actionIntensity = 0.9;
      } else if (segment.motionScore > motionP75) {
        actionType = 'medium';
        actionIntensity = 0.7;
      }

      // Enhanced action detection with face analysis
      if (segment.faceCount > 2 && segment.motionScore > 0.5) {
        actionType = 'social_interaction';
        actionIntensity = math.max(actionIntensity, 0.8);
      }

      if (actionIntensity > 0.6) {
        actionMoments.add(ActionMoment(
          timestamp: segment.startTime,
          duration: segment.duration,
          actionType: actionType,
          intensity: actionIntensity,
          confidence: _calculateActionConfidence(segment),
        ));
      }
    }

    return actionMoments;
  }

  /// Identify high-quality moments suitable for clipping
  static Future<List<QualityMoment>> _identifyQualityMoments(
    List<VideoSegmentAnalysis> segments,
  ) async {
    final List<QualityMoment> qualityMoments = [];

    // Calculate quality percentiles
    final qualityScores = segments.map((s) => s.qualityScore).toList();
    qualityScores.sort();
    final qualityP75 = qualityScores[(qualityScores.length * 0.75).floor()];

    for (final segment in segments) {
      if (segment.qualityScore > qualityP75 && segment.clipWorthiness > 0.7) {
        qualityMoments.add(QualityMoment(
          timestamp: segment.startTime,
          duration: segment.duration,
          qualityScore: segment.qualityScore,
          visualInterest: segment.visualInterest,
          stability: _calculateStability(segment),
        ));
      }
    }

    return qualityMoments;
  }

  /// Generate intelligent clip suggestions
  static Future<List<ClipSuggestion>> _generateClipSuggestions(
    List<VideoSegmentAnalysis> segments,
    List<SceneChangePoint> sceneChanges,
    List<ActionMoment> actionMoments,
    List<QualityMoment> qualityMoments,
  ) async {
    final List<ClipSuggestion> suggestions = [];

    // Group consecutive high-quality segments
    final List<List<VideoSegmentAnalysis>> groups =
        _groupConsecutiveSegments(segments);

    for (final group in groups) {
      if (group.length < 2) {
        continue; // Skip single segments
      }

      final startTime = group.first.startTime;
      final endTime = group.last.startTime + group.last.duration;
      final duration = endTime - startTime;

      if (duration < 3.0 || duration > 30.0) {
        continue; // Skip too short/long clips
      }

      final avgClipWorthiness =
          group.map((s) => s.clipWorthiness).reduce((a, b) => a + b) /
              group.length;
      final avgVisualInterest =
          group.map((s) => s.visualInterest).reduce((a, b) => a + b) /
              group.length;

      if (avgClipWorthiness > 0.6) {
        final reasons = <String>[];

        // Determine extraction reasons
        if (avgVisualInterest > 0.7) {
          reasons.add('high_visual_interest');
        }
        if (group.any((s) => s.faceCount > 1)) {
          reasons.add('social_interaction');
        }
        if (group.any((s) => s.motionScore > 0.7)) {
          reasons.add('high_activity');
        }
        if (group.any((s) => s.qualityScore > 0.8)) {
          reasons.add('excellent_quality');
        }

        // Check for scene boundaries
        final hasSceneChange = sceneChanges
            .any((sc) => sc.timestamp >= startTime && sc.timestamp <= endTime);
        if (hasSceneChange) {
          reasons.add('scene_transition');
        }

        // Check for action moments
        final hasActionMoment = actionMoments
            .any((am) => am.timestamp >= startTime && am.timestamp <= endTime);
        if (hasActionMoment) {
          reasons.add('action_peak');
        }

        suggestions.add(ClipSuggestion(
          startTime: startTime,
          endTime: endTime,
          duration: duration,
          confidence: avgClipWorthiness,
          priority: _calculatePriority(
              avgClipWorthiness, avgVisualInterest, reasons.length),
          extractionReasons: reasons,
          contentType: _determineContentType(group),
        ));
      }
    }

    // Sort by priority and limit to top suggestions
    suggestions.sort((a, b) => b.priority.compareTo(a.priority));
    return suggestions.take(10).toList();
  }

  // Helper methods
  static Future<double> _getActualVideoDuration(String videoPath) async {
    try {
      // Try to get file size as a rough estimate of duration
      final file = File(videoPath);
      final fileSizeBytes = await file.length();
      final fileSizeMB = fileSizeBytes / (1024 * 1024);

      // More conservative estimation for modern video compression
      // Assume ~5-20MB per minute depending on quality (use conservative 15MB)
      final estimatedDuration = math.max(fileSizeMB / 15.0 * 60.0, 30.0);

      // Be more conservative with caps to prevent excessive analysis
      final cappedDuration =
          math.min(estimatedDuration, 900.0); // Max 15 minutes estimate

      debugPrint(
          'Video file size: ${fileSizeMB.toStringAsFixed(1)}MB, estimated duration: ${cappedDuration.toStringAsFixed(1)}s');

      return cappedDuration;
    } catch (e) {
      debugPrint('Could not estimate video duration: $e');
      // Fallback to a smaller default for analysis
      return 120.0; // 2 minutes default (reduced from 5 minutes)
    }
  }

  static double _calculateActivityLevel(double motionScore, int faceCount) {
    final motionComponent = motionScore * 0.7;
    final socialComponent = math.min(faceCount / 5.0, 1.0) * 0.3;
    return math.min(motionComponent + socialComponent, 1.0);
  }

  static double _calculateVisualInterest(
      double qualityScore, double motionScore, int faceCount) {
    final qualityComponent = qualityScore * 0.4;
    final motionComponent = motionScore * 0.4;
    final faceComponent = math.min(faceCount / 3.0, 1.0) * 0.2;
    return math.min(qualityComponent + motionComponent + faceComponent, 1.0);
  }

  static double _calculateClipWorthiness(Map<String, dynamic> analysisResults) {
    final motionScore = analysisResults['motion_score'] ?? 0.0;
    final qualityScore = analysisResults['quality_score'] ?? 0.0;
    final faceCount = analysisResults['face_count'] ?? 0;

    // Advanced scoring algorithm
    final baseScore = (motionScore * 0.3 + qualityScore * 0.5) * 0.8;
    final socialBonus =
        faceCount > 0 ? math.min<double>(faceCount / 5.0, 0.2) : 0.0;

    return math.min(baseScore + socialBonus, 1.0);
  }

  static String _determineChangeType(
      double motionDiff, double qualityDiff, int faceDiff) {
    if (motionDiff > 0.5) {
      return 'action_change';
    }
    if (qualityDiff > 0.4) {
      return 'quality_change';
    }
    if (faceDiff > 2) {
      return 'social_change';
    }
    return 'gradual_change';
  }

  static double _calculateActionConfidence(VideoSegmentAnalysis segment) {
    final motionConfidence = segment.motionScore;
    final qualityConfidence = segment.qualityScore;
    final socialConfidence = segment.faceCount > 0 ? 0.8 : 0.3;

    return (motionConfidence * 0.5 +
        qualityConfidence * 0.3 +
        socialConfidence * 0.2);
  }

  static double _calculateStability(VideoSegmentAnalysis segment) {
    // Calculate stability based on motion score (lower motion = higher stability)
    // Higher motion scores indicate more camera shake or instability
    return 1.0 - math.min(segment.motionScore / 2.0, 0.5);
  }

  static List<List<VideoSegmentAnalysis>> _groupConsecutiveSegments(
      List<VideoSegmentAnalysis> segments) {
    final List<List<VideoSegmentAnalysis>> groups = [];
    List<VideoSegmentAnalysis> currentGroup = [];

    for (final segment in segments) {
      if (segment.clipWorthiness > 0.5) {
        currentGroup.add(segment);
      } else {
        if (currentGroup.isNotEmpty) {
          groups.add(List.from(currentGroup));
          currentGroup.clear();
        }
      }
    }

    if (currentGroup.isNotEmpty) {
      groups.add(currentGroup);
    }

    return groups;
  }

  static double _calculatePriority(
      double clipWorthiness, double visualInterest, int reasonCount) {
    final baseScore = (clipWorthiness * 0.6 + visualInterest * 0.4);
    final reasonBonus = math.min(reasonCount / 5.0, 0.3);
    return math.min(baseScore + reasonBonus, 1.0);
  }

  static String _determineContentType(List<VideoSegmentAnalysis> group) {
    final avgFaceCount =
        group.map((s) => s.faceCount).reduce((a, b) => a + b) / group.length;
    final avgMotion =
        group.map((s) => s.motionScore).reduce((a, b) => a + b) / group.length;

    if (avgFaceCount > 2) {
      return 'social_interaction';
    }
    if (avgMotion > 0.7) {
      return 'high_action';
    }
    if (avgMotion < 0.3) {
      return 'calm_scene';
    }
    return 'general_content';
  }

  static OverallMetrics _calculateOverallMetrics(
      List<VideoSegmentAnalysis> segments) {
    if (segments.isEmpty) {
      return OverallMetrics(
        averageMotion: 0.0,
        averageQuality: 0.0,
        totalFaces: 0,
        activityLevel: 'low',
        overallScore: 0.0,
      );
    }

    final avgMotion =
        segments.map((s) => s.motionScore).reduce((a, b) => a + b) /
            segments.length;
    final avgQuality =
        segments.map((s) => s.qualityScore).reduce((a, b) => a + b) /
            segments.length;
    final totalFaces = segments.map((s) => s.faceCount).reduce((a, b) => a + b);

    String activityLevel = 'low';
    if (avgMotion > 0.7) {
      activityLevel = 'high';
    } else if (avgMotion > 0.4) {
      activityLevel = 'medium';
    }

    final overallScore = (avgMotion * 0.4 + avgQuality * 0.6);

    return OverallMetrics(
      averageMotion: avgMotion,
      averageQuality: avgQuality,
      totalFaces: totalFaces,
      activityLevel: activityLevel,
      overallScore: overallScore,
    );
  }

  static Future<void> _ensureInitialized() async {
    if (!_initialized) {
      final success = await initialize();
      if (!success) {
        throw Exception('Enhanced OpenCV Analysis Service not initialized');
      }
    }
  }
}

// Data classes for structured analysis results

class VideoAnalysisResult {
  final String videoPath;
  final double duration;
  final List<VideoSegmentAnalysis> segmentAnalyses;
  final List<SceneChangePoint> sceneChanges;
  final List<ActionMoment> actionMoments;
  final List<QualityMoment> qualityMoments;
  final List<ClipSuggestion> clipSuggestions;
  final OverallMetrics overallMetrics;

  VideoAnalysisResult({
    required this.videoPath,
    required this.duration,
    required this.segmentAnalyses,
    required this.sceneChanges,
    required this.actionMoments,
    required this.qualityMoments,
    required this.clipSuggestions,
    required this.overallMetrics,
  });

  Map<String, dynamic> toJson() {
    return {
      'video_path': videoPath,
      'duration': duration,
      'segment_count': segmentAnalyses.length,
      'scene_changes': sceneChanges.map((sc) => sc.toJson()).toList(),
      'action_moments': actionMoments.map((am) => am.toJson()).toList(),
      'quality_moments': qualityMoments.map((qm) => qm.toJson()).toList(),
      'clip_suggestions': clipSuggestions.map((cs) => cs.toJson()).toList(),
      'overall_metrics': overallMetrics.toJson(),
    };
  }
}

class VideoSegmentAnalysis {
  final double startTime;
  final double duration;
  final double motionScore;
  final double qualityScore;
  final int faceCount;
  final double activityLevel;
  final double visualInterest;
  final double clipWorthiness;
  final Map<String, dynamic> rawResults;

  VideoSegmentAnalysis({
    required this.startTime,
    required this.duration,
    required this.motionScore,
    required this.qualityScore,
    required this.faceCount,
    required this.activityLevel,
    required this.visualInterest,
    required this.clipWorthiness,
    required this.rawResults,
  });
}

class SceneChangePoint {
  final double timestamp;
  final double changeScore;
  final String changeType;
  final double confidence;

  SceneChangePoint({
    required this.timestamp,
    required this.changeScore,
    required this.changeType,
    required this.confidence,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp,
      'change_score': changeScore,
      'change_type': changeType,
      'confidence': confidence,
    };
  }
}

class ActionMoment {
  final double timestamp;
  final double duration;
  final String actionType;
  final double intensity;
  final double confidence;

  ActionMoment({
    required this.timestamp,
    required this.duration,
    required this.actionType,
    required this.intensity,
    required this.confidence,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp,
      'duration': duration,
      'action_type': actionType,
      'intensity': intensity,
      'confidence': confidence,
    };
  }
}

class QualityMoment {
  final double timestamp;
  final double duration;
  final double qualityScore;
  final double visualInterest;
  final double stability;

  QualityMoment({
    required this.timestamp,
    required this.duration,
    required this.qualityScore,
    required this.visualInterest,
    required this.stability,
  });

  Map<String, dynamic> toJson() {
    return {
      'timestamp': timestamp,
      'duration': duration,
      'quality_score': qualityScore,
      'visual_interest': visualInterest,
      'stability': stability,
    };
  }
}

class ClipSuggestion {
  final double startTime;
  final double endTime;
  final double duration;
  final double confidence;
  final double priority;
  final List<String> extractionReasons;
  final String contentType;

  ClipSuggestion({
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.confidence,
    required this.priority,
    required this.extractionReasons,
    required this.contentType,
  });

  Map<String, dynamic> toJson() {
    return {
      'start_time': startTime,
      'end_time': endTime,
      'duration': duration,
      'confidence': confidence,
      'priority': priority,
      'extraction_reasons': extractionReasons,
      'content_type': contentType,
    };
  }
}

class OverallMetrics {
  final double averageMotion;
  final double averageQuality;
  final int totalFaces;
  final String activityLevel;
  final double overallScore;

  OverallMetrics({
    required this.averageMotion,
    required this.averageQuality,
    required this.totalFaces,
    required this.activityLevel,
    required this.overallScore,
  });

  Map<String, dynamic> toJson() {
    return {
      'average_motion': averageMotion,
      'average_quality': averageQuality,
      'total_faces': totalFaces,
      'activity_level': activityLevel,
      'overall_score': overallScore,
    };
  }
}
