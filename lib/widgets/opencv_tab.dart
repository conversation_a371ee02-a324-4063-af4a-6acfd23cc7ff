import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:file_picker/file_picker.dart';
import 'package:share_plus/share_plus.dart';
import '../services/opencv_video_analysis_service_ffi.dart';
import '../services/enhanced_opencv_analysis_service.dart';

class OpenCVTab extends StatefulWidget {
  const OpenCVTab({super.key});

  @override
  State<OpenCVTab> createState() => _OpenCVTabState();
}

class _OpenCVTabState extends State<OpenCVTab> with TickerProviderStateMixin {
  String _openCVVersion = 'Unknown';
  String _analysisResult = '';
  String? _selectedVideoPath;
  String _selectedFileName = '';
  bool _isAnalyzing = false;
  bool _isInitialized = false;
  bool _hasSelectedFile = false;
  String _currentAnalysisStep = '';
  double _analysisProgress = 0.0;
  int _currentStepIndex = 0;

  final List<Map<String, dynamic>> _meaningfulSegments = [];
  final List<Map<String, dynamic>> _highlights = [];
  Map<String, dynamic>? _videoInfo;
  Map<String, dynamic>? _motionAnalysis;
  Map<String, dynamic>? _qualityAnalysis;

  late AnimationController _pulseController;
  late AnimationController _spinnerController;
  late AnimationController _progressController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _spinnerAnimation;
  late Animation<double> _progressAnimation;
  late TabController _tabController;

  final List<String> _analysisSteps = [
    'Initializing analysis...',
    'Reading video metadata...',
    'Analyzing motion & scenes...',
    'Assessing quality...',
    'Detecting faces & activity...',
    'Finding action moments...',
    'Processing transitions...',
    'Computing metrics...',
    'Finalizing analysis...',
  ];

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _tabController = TabController(length: 4, vsync: this);
    _initializeOpenCV();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _spinnerController.dispose();
    _progressController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    // Pulse animation for the analyze button
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.08,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Smooth spinner animation
    _spinnerController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _spinnerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _spinnerController,
      curve: Curves.easeInOut,
    ));

    // Progress animation
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOut,
    ));
  }

  Future<void> _initializeOpenCV() async {
    try {
      // Initialize both basic and enhanced services
      await OpenCVVideoAnalysisService.initialize();
      await EnhancedOpenCVAnalysisService.initialize();
      final version = OpenCVVideoAnalysisService.getVersion();

      setState(() {
        _isInitialized = true;
        _openCVVersion = version;
      });
      _showSuccessSnackBar('Enhanced OpenCV Analysis Service initialized!');
    } catch (e) {
      setState(() {
        _isInitialized = false;
        _openCVVersion = 'Error: $e';
      });
      _showErrorSnackBar('OpenCV initialization failed: $e');
    }
  }

  Future<void> _pickVideoFile() async {
    try {
      // Add loading state
      setState(() {
        _analysisResult = 'Opening file picker...';
      });

      // Clear temporary files if possible (helps with iOS/Android)
      if (Platform.isIOS || Platform.isAndroid) {
        try {
          await FilePicker.platform.clearTemporaryFiles();
        } catch (e) {
          debugPrint('Warning: Could not clear temporary files: $e');
        }
      }

      // Add timeout protection
      FilePickerResult? result;
      try {
        result = await FilePicker.platform
            .pickFiles(
          type: FileType.video,
          allowMultiple: false,
          withData: false, // Don't load file data into memory
          withReadStream: false, // Don't create read stream
          allowedExtensions: null, // Let it auto-detect video types
          allowCompression: false, // Don't compress
          lockParentWindow: true, // Helps with desktop platforms
        )
            .timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            _showErrorSnackBar('File picker timed out. Please try again.');
            throw TimeoutException(
                'File picker timeout', const Duration(seconds: 30));
          },
        );
      } on TimeoutException {
        setState(() {
          _analysisResult = '';
        });
        return;
      }

      // Clear loading state
      setState(() {
        _analysisResult = '';
      });

      if (result != null && result.files.isNotEmpty) {
        final pickedFile = result.files.first;

        // Check if we have a valid path
        if (pickedFile.path == null || pickedFile.path!.isEmpty) {
          _showErrorSnackBar('Could not access the selected file path.');
          return;
        }

        final file = File(pickedFile.path!);

        // Verify file exists and is accessible
        if (!await file.exists()) {
          _showErrorSnackBar(
              'Selected file is not accessible or does not exist.');
          return;
        }

        // Check file size (optional - prevent huge files)
        final fileSize = await file.length();
        final fileSizeMB = fileSize / (1024 * 1024);

        if (fileSizeMB > 500) {
          // 500MB limit
          _showErrorSnackBar(
              'File is too large (${fileSizeMB.toStringAsFixed(1)}MB). Please select a smaller video.');
          return;
        }

        setState(() {
          _selectedVideoPath = pickedFile.path!;
          _selectedFileName = pickedFile.name;
          _hasSelectedFile = true;
          _analysisResult = '';
          _meaningfulSegments.clear();
          _highlights.clear();
          _videoInfo = null;
          _motionAnalysis = null;
          _qualityAnalysis = null;
        });

        _showSuccessSnackBar(
            'Video file selected successfully! (${fileSizeMB.toStringAsFixed(1)}MB)');

        debugPrint('Selected video: ${pickedFile.name} at ${pickedFile.path}');
      } else {
        // User cancelled or no file selected
        debugPrint('No video file selected - user cancelled or picker failed');
        setState(() {
          _analysisResult = '';
        });
      }
    } on PlatformException catch (e) {
      setState(() {
        _analysisResult = '';
      });

      // Handle specific platform errors
      String errorMessage;
      if (e.code == 'read_external_storage_denied') {
        errorMessage =
            'Storage permission denied. Please enable storage access in Settings.';
      } else if (e.code == 'photo_access_denied') {
        errorMessage =
            'Photo library access denied. Please enable photo access in Settings.';
      } else if (e.message?.toLowerCase().contains('permission') == true) {
        errorMessage =
            'Permission denied. Please check app permissions in Settings and try again.';
      } else {
        errorMessage = 'Platform error: ${e.message ?? e.code}';
      }

      _showErrorSnackBar(errorMessage);
      debugPrint('PlatformException in file picker: ${e.toString()}');
    } catch (e) {
      setState(() {
        _analysisResult = '';
      });
      _showErrorSnackBar('Error selecting video file: $e');
      debugPrint('General error in file picker: ${e.toString()}');
    }
  }

  Future<void> _runOpenCVAnalysis() async {
    if (!_isInitialized) {
      _showErrorSnackBar(
          'OpenCV not initialized. Please wait for initialization.');
      return;
    }

    if (_selectedVideoPath == null) {
      _showErrorSnackBar('Please select a video file first.');
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _analysisResult = '';
      _currentStepIndex = 0;
      _analysisProgress = 0.0;
      _currentAnalysisStep = _analysisSteps[0];
    });

    // Start beautiful animations
    _pulseController.repeat(reverse: true);
    _spinnerController.repeat();

    try {
      // Step through the analysis with visual feedback
      await _updateAnalysisStep(0, 'Initializing analysis...');
      await Future.delayed(const Duration(milliseconds: 500));

      await _updateAnalysisStep(1, 'Reading video metadata...');
      await Future.delayed(const Duration(milliseconds: 300));

      await _updateAnalysisStep(2, 'Analyzing motion & scene changes...');
      await Future.delayed(const Duration(milliseconds: 400));

      await _updateAnalysisStep(3, 'Assessing quality...');
      await Future.delayed(const Duration(milliseconds: 300));

      await _updateAnalysisStep(4, 'Detecting faces & activity...');
      await Future.delayed(const Duration(milliseconds: 300));

      await _updateAnalysisStep(5, 'Identifying action moments...');
      await Future.delayed(const Duration(milliseconds: 400));

      await _updateAnalysisStep(6, 'Processing scene transitions...');
      await Future.delayed(const Duration(milliseconds: 300));

      await _updateAnalysisStep(7, 'Computing metrics...');

      // Add extra yield to prevent UI blocking
      await Future.delayed(const Duration(milliseconds: 100));

      // Perform the enhanced comprehensive analysis with progress callback
      final enhancedAnalysisResult =
          await EnhancedOpenCVAnalysisService.performComprehensiveVideoAnalysis(
        _selectedVideoPath!,
        analysisInterval: 5.0, // Analyze every 5 seconds (optimized)
        segmentDuration: 3.0, // 3-second segments (optimized)
        enableSceneDetection: true,
        enableMotionAnalysis: true,
        enableQualityAssessment: true,
        enableFaceDetection: true,
        enableActivityAnalysis: true,
        progressCallback: (progress) {
          // Update progress bar with segment analysis progress
          if (mounted) {
            setState(() {
              _analysisProgress =
                  0.7 + (progress * 0.25); // Map to 70-95% of total progress
              _currentAnalysisStep =
                  'Processing segment ${(progress * 100).toInt()}% complete...';
            });
          }
        },
      );

      await _updateAnalysisStep(8, 'Finalizing intelligent analysis...');
      await Future.delayed(const Duration(milliseconds: 300));

      // Complete the analysis
      if (mounted) {
        setState(() {
          _analysisResult = _formatEnhancedResults(enhancedAnalysisResult);
          _isAnalyzing = false;
          _currentAnalysisStep = 'Analysis complete!';
          _analysisProgress = 1.0;
        });
      }

      _stopAnimations();

      // Small delay before showing success message to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 200));

      if (mounted) {
        _showSuccessSnackBar(
            '🎉 Enhanced video analysis completed successfully!');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isAnalyzing = false;
          _analysisResult = 'Analysis failed: $e';
          _analysisProgress = 0.0;
          _currentAnalysisStep = 'Analysis failed';
        });
      }

      _stopAnimations();

      if (mounted) {
        _showErrorSnackBar('❌ Video analysis failed. Please try again.');
      }
    }
  }

  Future<void> _updateAnalysisStep(int stepIndex, String message) async {
    setState(() {
      _currentStepIndex = stepIndex;
      _currentAnalysisStep = message;
      _analysisProgress = (stepIndex + 1) / _analysisSteps.length;
    });

    // Animate progress smoothly
    _progressController.reset();
    await _progressController.forward();
  }

  void _stopAnimations() {
    _pulseController.stop();
    _pulseController.reset();
    _spinnerController.stop();
    _spinnerController.reset();
  }

  Future<void> _shareResults() async {
    if (_analysisResult.isNotEmpty) {
      await Share.share(_analysisResult, subject: 'OpenCV Video Analysis');
    }
  }

  Future<void> _copyToClipboard() async {
    if (_analysisResult.isNotEmpty) {
      await Clipboard.setData(ClipboardData(text: _analysisResult));
      _showSuccessSnackBar('Copied to clipboard!');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height - 180,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).size.height - 180,
          left: 20,
          right: 20,
        ),
      ),
    );
  }

  String _formatEnhancedResults(VideoAnalysisResult result) {
    final buffer = StringBuffer();

    buffer.writeln('🎉 Enhanced OpenCV Analysis Completed!\n');

    // Video Overview
    buffer.writeln('📹 Video Analysis Overview:');
    buffer.writeln('   Duration: ${result.duration.toStringAsFixed(1)}s');
    buffer.writeln('   Segments Analyzed: ${result.segmentAnalyses.length}');
    buffer.writeln(
        '   Total Faces Detected: ${result.overallMetrics.totalFaces}');
    buffer
        .writeln('   Activity Level: ${result.overallMetrics.activityLevel}\n');

    // Scene Changes
    if (result.sceneChanges.isNotEmpty) {
      buffer.writeln(
          '🎬 Scene Changes (${result.sceneChanges.length} detected):');
      for (int i = 0; i < result.sceneChanges.length; i++) {
        final change = result.sceneChanges[i];
        final minutes = (change.timestamp / 60).floor();
        final seconds = (change.timestamp % 60).floor();
        buffer.writeln(
            '   ${i + 1}. ${_getChangeEmoji(change.changeType)} ${change.changeType}');
        buffer.writeln(
            '      Time: $minutes:${seconds.toString().padLeft(2, '0')}');
      }
      buffer.writeln('');
    }

    // Action Moments
    if (result.actionMoments.isNotEmpty) {
      buffer.writeln(
          '🏃 Action Moments (${result.actionMoments.length} detected):');
      for (int i = 0; i < result.actionMoments.length; i++) {
        final action = result.actionMoments[i];
        final minutes = (action.timestamp / 60).floor();
        final seconds = (action.timestamp % 60).floor();
        buffer.writeln(
            '   ${i + 1}. ${_getActionEmoji(action.actionType)} ${action.actionType}');
        buffer.writeln(
            '      Time: $minutes:${seconds.toString().padLeft(2, '0')} (${action.duration.toStringAsFixed(1)}s)');
      }
      buffer.writeln('');
    }

    // Quality Moments
    if (result.qualityMoments.isNotEmpty) {
      buffer.writeln(
          '🌟 Quality Moments (${result.qualityMoments.length} detected):');
      for (int i = 0; i < result.qualityMoments.length; i++) {
        final quality = result.qualityMoments[i];
        final minutes = (quality.timestamp / 60).floor();
        final seconds = (quality.timestamp % 60).floor();
        buffer.writeln('   ${i + 1}. High Quality Segment');
        buffer.writeln(
            '      Time: $minutes:${seconds.toString().padLeft(2, '0')} (${quality.duration.toStringAsFixed(1)}s)');
      }
      buffer.writeln('');
    }

    // Segment Analysis Summary
    buffer.writeln('📈 Segment Analysis Summary:');
    final topSegments = result.segmentAnalyses
        .where((s) => s.clipWorthiness > 0.6)
        .toList()
      ..sort((a, b) => b.clipWorthiness.compareTo(a.clipWorthiness));

    if (topSegments.isNotEmpty) {
      buffer.writeln('   All ${topSegments.length} High-Quality Segments:');
      for (int i = 0; i < topSegments.length; i++) {
        final segment = topSegments[i];
        final minutes = (segment.startTime / 60).floor();
        final seconds = (segment.startTime % 60).floor();
        buffer.writeln(
            '   ${i + 1}. ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')} (${segment.duration.toStringAsFixed(1)}s)');
        buffer.writeln('      Faces: ${segment.faceCount}');
      }
    } else {
      buffer.writeln('   No high-worthiness segments detected');
    }

    // Complete Segment Analysis - All Segments
    buffer.writeln('\n📊 Complete Segment Analysis:');
    buffer
        .writeln('   All ${result.segmentAnalyses.length} Analyzed Segments:');
    for (int i = 0; i < result.segmentAnalyses.length; i++) {
      final segment = result.segmentAnalyses[i];
      final minutes = (segment.startTime / 60).floor();
      final seconds = (segment.startTime % 60).floor();
      buffer.writeln(
          '   ${i + 1}. ${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')} (${segment.duration.toStringAsFixed(1)}s)');
      buffer.writeln('      Faces: ${segment.faceCount}');
    }

    buffer.writeln('\n✅ Enhanced analysis completed successfully!');
    buffer.writeln('🔧 Using Advanced OpenCV Intelligence Pipeline');

    return buffer.toString();
  }

  String _getChangeEmoji(String changeType) {
    switch (changeType.toLowerCase()) {
      case 'cut':
        return '✂️';
      case 'fade':
        return '🌅';
      case 'scene_transition':
        return '🎬';
      case 'lighting_change':
        return '💡';
      default:
        return '🔄';
    }
  }

  String _getActionEmoji(String actionType) {
    switch (actionType.toLowerCase()) {
      case 'high_motion':
        return '🏃‍♂️';
      case 'interaction':
        return '👥';
      case 'speaking':
        return '🗣️';
      case 'gesture':
        return '👋';
      case 'movement':
        return '🚶‍♂️';
      default:
        return '⚡';
    }
  }

  // String _formatComprehensiveResults() {
  //   final buffer = StringBuffer();

  //   buffer.writeln('🎉 Comprehensive OpenCV Analysis Completed!\n');

  //   // Video Information
  //   if (_videoInfo != null) {
  //     buffer.writeln('📹 Video Information:');
  //     buffer.writeln(
  //         '   Duration: ${_videoInfo!['duration']?.toStringAsFixed(2) ?? 'N/A'}s');
  //     buffer.writeln('   Resolution: ${_videoInfo!['resolution'] ?? 'N/A'}');
  //     buffer.writeln(
  //         '   FPS: ${_videoInfo!['fps']?.toStringAsFixed(1) ?? 'N/A'}');
  //     buffer.writeln('   Codec: ${_videoInfo!['codec'] ?? 'N/A'}\n');
  //   }

  //   // Meaningful Segments
  //   buffer.writeln(
  //       '🎯 Meaningful Segments (${_meaningfulSegments.length} found):');
  //   if (_meaningfulSegments.isNotEmpty) {
  //     for (int i = 0; i < _meaningfulSegments.length; i++) {
  //       final segment = _meaningfulSegments[i];
  //       final timestamp = segment['timestamp'] ?? 0.0;
  //       final duration = segment['duration'] ?? 0.0;
  //       final score = segment['compositeScore'] ?? 0.0;
  //       final reason = segment['extractionReason'] ?? 'unknown';

  //       final minutes = (timestamp / 60).floor();
  //       final seconds = (timestamp % 60).floor();

  //       buffer.writeln('   ${i + 1}. ${_getReasonEmoji(reason)} $reason');
  //       buffer.writeln(
  //           '      Time: $minutes:${seconds.toString().padLeft(2, '0')} (${duration.toStringAsFixed(1)}s)');
  //       buffer.writeln('      Score: ${(score * 100).toStringAsFixed(1)}%');
  //     }
  //     buffer.writeln('');
  //   }

  //   // Quality Analysis
  //   if (_qualityAnalysis != null) {
  //     buffer.writeln('🔍 Quality Analysis:');
  //     buffer.writeln(
  //         '   Overall Score: ${((_qualityAnalysis!['overallScore'] ?? 0.0) * 100).toStringAsFixed(1)}%');
  //     buffer.writeln(
  //         '   Sharpness: ${((_qualityAnalysis!['sharpness'] ?? 0.0) * 100).toStringAsFixed(1)}%');
  //     buffer.writeln(
  //         '   Brightness: ${((_qualityAnalysis!['brightness'] ?? 0.0) * 100).toStringAsFixed(1)}%');
  //     buffer.writeln(
  //         '   Contrast: ${((_qualityAnalysis!['contrast'] ?? 0.0) * 100).toStringAsFixed(1)}%');
  //     buffer.writeln(
  //         '   Colorfulness: ${((_qualityAnalysis!['colorfulness'] ?? 0.0) * 100).toStringAsFixed(1)}%\n');
  //   }

  //   // Motion Analysis
  //   if (_motionAnalysis != null) {
  //     buffer.writeln('🏃 Motion Analysis:');
  //     buffer.writeln(
  //         '   Average Intensity: ${((_motionAnalysis!['averageIntensity'] ?? 0.0) * 100).toStringAsFixed(1)}%');
  //     buffer.writeln(
  //         '   Max Intensity: ${((_motionAnalysis!['maxIntensity'] ?? 0.0) * 100).toStringAsFixed(1)}%');
  //     buffer.writeln(
  //         '   Variance: ${(_motionAnalysis!['variance'] ?? 0.0).toStringAsFixed(3)}');
  //     buffer.writeln(
  //         '   Motion Events: ${_motionAnalysis!['eventCount'] ?? 0}\n');
  //   }

  //   // Highlights
  //   buffer.writeln('✨ Highlights (${_highlights.length} found):');
  //   if (_highlights.isNotEmpty) {
  //     for (int i = 0; i < _highlights.length; i++) {
  //       final highlight = _highlights[i];
  //       final timestamp = highlight['timestamp'] ?? 0.0;
  //       final confidence = highlight['confidence'] ?? 0.0;
  //       final type = highlight['type'] ?? 'unknown';

  //       final minutes = (timestamp / 60).floor();
  //       final seconds = (timestamp % 60).floor();

  //       buffer.writeln('   ${i + 1}. ${_getAnalysisEmoji(type)} $type');
  //       buffer.writeln(
  //           '      Time: $minutes:${seconds.toString().padLeft(2, '0')}');
  //       buffer.writeln(
  //           '      Confidence: ${(confidence * 100).toStringAsFixed(1)}%');
  //     }
  //   }

  //   buffer.writeln('\n✅ Comprehensive analysis completed successfully!');
  //   return buffer.toString();
  // }

  String _getReasonEmoji(String reason) {
    switch (reason.toLowerCase()) {
      case 'high_quality':
        return '🌟';
      case 'high_motion':
        return '🏃';
      case 'high_engagement':
        return '🎯';
      case 'face_interaction':
        return '👥';
      case 'balanced_content':
        return '⚖️';
      default:
        return '📊';
    }
  }

  // String _formatAnalysisResults(
  //     Map<String, dynamic> videoInfo, List<Map<String, dynamic>> results) {
  //   final buffer = StringBuffer();

  //   buffer.writeln('🎉 OpenCV Analysis Completed!\n');

  //   // Video Information - with safe type casting
  //   buffer.writeln('📹 Video Information:');

  //   // Safe duration formatting
  //   final duration = videoInfo['duration'];
  //   final durationStr =
  //       duration is num ? duration.toDouble().toStringAsFixed(2) : 'N/A';
  //   buffer.writeln('   Duration: ${durationStr}s');

  //   buffer.writeln('   Width: ${videoInfo['width'] ?? 'N/A'}');
  //   buffer.writeln('   Height: ${videoInfo['height'] ?? 'N/A'}');

  //   // Safe FPS formatting
  //   final fps = videoInfo['fps'];
  //   final fpsStr = fps is num ? fps.toDouble().toStringAsFixed(1) : 'N/A';
  //   buffer.writeln('   FPS: $fpsStr');

  //   buffer.writeln('   Total Frames: ${videoInfo['frameCount'] ?? 'N/A'}\n');

  //   if (results.isEmpty) {
  //     buffer.writeln('🔍 Analysis Results: No highlights detected\n');
  //     buffer.writeln('This could be normal for videos with:');
  //     buffer.writeln('• Low motion content');
  //     buffer.writeln('• Static scenes');
  //     buffer.writeln('• Short duration');
  //   } else {
  //     buffer.writeln(
  //         '🔍 Analysis Results (${results.length} highlights detected):\n');

  //     for (int i = 0; i < results.length; i++) {
  //       final result = results[i];

  //       // Safe type casting for timestamp
  //       final timestampValue = result['timestamp'];
  //       final timestamp =
  //           timestampValue is num ? timestampValue.toDouble() : 0.0;

  //       // Safe type casting for confidence
  //       final confidenceValue = result['confidence'];
  //       final confidence =
  //           confidenceValue is num ? confidenceValue.toDouble() : 0.0;

  //       final type = result['type']?.toString() ?? 'unknown';

  //       final minutes = (timestamp / 60).floor();
  //       final seconds = (timestamp % 60).floor();

  //       buffer.writeln(
  //           '${i + 1}. ${_getAnalysisEmoji(type)} ${_getAnalysisDescription(type)}');
  //       buffer
  //           .writeln('   Time: $minutes:${seconds.toString().padLeft(2, '0')}');
  //       buffer.writeln(
  //           '   Confidence: ${(confidence * 100).toStringAsFixed(1)}%\n');
  //     }
  //   }

  //   buffer.writeln('✅ OpenCV plugin successfully executed!');
  //   return buffer.toString();
  // }

  String _getAnalysisEmoji(String type) {
    switch (type.toLowerCase()) {
      case 'motion':
        return '🏃';
      case 'highlight':
        return '✨';
      case 'scene_change':
        return '🎬';
      case 'object_detection':
        return '🎯';
      default:
        return '📊';
    }
  }

  String _getAnalysisDescription(String type) {
    switch (type.toLowerCase()) {
      case 'motion':
        return 'Motion Detected';
      case 'highlight':
        return 'Potential Highlight';
      case 'scene_change':
        return 'Scene Change';
      case 'object_detection':
        return 'Object Detected';
      default:
        return 'Analysis Result';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              // Header
              _buildHeader(),
              const SizedBox(height: 30),

              // Main Action Area
              _buildMainActionArea(),
              const SizedBox(height: 30),

              // Results Section
              _buildResultsSection(),

              // Analysis Summary
              if (_meaningfulSegments.isNotEmpty) ...[
                const SizedBox(height: 20),
                _buildAnalysisSummary(),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF11998e), Color(0xFF38ef7d)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            Icons.video_camera_back,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          const Text(
            'AI Video Analysis',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Powered by OpenCV • Version $_openCVVersion',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _isInitialized ? Icons.check_circle : Icons.hourglass_empty,
                color: _isInitialized ? Colors.green : Colors.orange,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                _isInitialized ? 'Ready to analyze' : 'Initializing...',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainActionArea() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // File Selection
          if (!_hasSelectedFile) ...[
            Icon(
              Icons.video_library,
              size: 60,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Select a video file to analyze',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'OpenCV will analyze motion, highlights, and scene changes',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _isInitialized ? _pickVideoFile : null,
              icon: const Icon(Icons.video_library),
              label: const Text('Choose Video File'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF11998e),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 5,
              ),
            ),
          ] else ...[
            // Selected File Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.video_file, color: Colors.green),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedFileName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const Text(
                          'Ready to analyze',
                          style: TextStyle(
                            color: Colors.green,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => setState(() => _hasSelectedFile = false),
                    icon: const Icon(Icons.close, color: Colors.grey),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Analyze Button
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _isAnalyzing ? _pulseAnimation.value : 1.0,
                  child: ElevatedButton.icon(
                    onPressed: _isAnalyzing ? null : _runOpenCVAnalysis,
                    icon: _isAnalyzing
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Icon(Icons.analytics),
                    label:
                        Text(_isAnalyzing ? 'Analyzing...' : 'Start Analysis'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF38ef7d),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 48, vertical: 18),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30),
                      ),
                      elevation: 8,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: _pickVideoFile,
              child: const Text('Choose Different Video'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_analysisResult.isEmpty && !_isAnalyzing) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header card
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(Icons.analytics, color: Color(0xFF11998e), size: 24),
              const SizedBox(width: 12),
              const Text(
                'Analysis Results',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_analysisResult.isNotEmpty) ...[
                IconButton(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy, size: 22),
                  tooltip: 'Copy to clipboard',
                ),
                IconButton(
                  onPressed: _shareResults,
                  icon: const Icon(Icons.share, size: 22),
                  tooltip: 'Share results',
                ),
              ],
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Results content - flows naturally with page scroll
        if (_isAnalyzing)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(40),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: _buildAppleStyleLoader(),
          )
        else if (_analysisResult.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: SelectableText(
              _analysisResult,
              style: const TextStyle(
                fontSize: 16,
                height: 1.6,
                color: Colors.black87,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildAnalysisSummary() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.green.shade50, Colors.green.shade100],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.summarize, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Text(
                'Analysis Summary',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade700,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSummaryRow(
              'Total meaningful segments', '${_meaningfulSegments.length}'),
          _buildSummaryRow('Total highlights', '${_highlights.length}'),
          if (_videoInfo != null) ...[
            _buildSummaryRow('Video duration',
                '${_videoInfo!['duration']?.toStringAsFixed(1) ?? 'N/A'}s'),
            _buildSummaryRow('Resolution',
                '${_videoInfo!['width'] ?? 'N/A'} × ${_videoInfo!['height'] ?? 'N/A'}'),
            _buildSummaryRow('Frame rate',
                '${_videoInfo!['fps']?.toStringAsFixed(1) ?? 'N/A'} FPS'),
          ],
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green.shade600, size: 16),
              const SizedBox(width: 8),
              Text(
                'Analysis completed successfully',
                style: TextStyle(
                  color: Colors.green.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.green.shade700,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComprehensiveResults() {
    return DefaultTabController(
      length: 4,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TabBar(
            tabs: const [
              Tab(icon: Icon(Icons.video_library), text: 'Segments'),
              Tab(icon: Icon(Icons.high_quality), text: 'Quality'),
              Tab(icon: Icon(Icons.motion_photos_on), text: 'Motion'),
              Tab(icon: Icon(Icons.highlight), text: 'Highlights'),
            ],
            labelColor: const Color(0xFF11998e),
            unselectedLabelColor: Colors.grey,
            indicatorColor: const Color(0xFF11998e),
          ),
          // Use a calculated height based on content
          SizedBox(
            height: 500, // Fixed reasonable height for tabs content
            child: TabBarView(
              children: [
                _buildSegmentsTab(),
                _buildQualityTab(),
                _buildMotionTab(),
                _buildHighlightsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSegmentsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _meaningfulSegments.length,
      itemBuilder: (context, index) {
        final segment = _meaningfulSegments[index];
        final timestamp = segment['timestamp'] ?? 0.0;
        final duration = segment['duration'] ?? 0.0;
        final score = segment['compositeScore'] ?? 0.0;
        final reason = segment['extractionReason'] ?? 'unknown';

        final minutes = (timestamp / 60).floor();
        final seconds = (timestamp % 60).floor();

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: const Color(0xFF11998e),
              child: Text('${index + 1}'),
            ),
            title: Text('${_getReasonEmoji(reason)} $reason'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    'Time: $minutes:${seconds.toString().padLeft(2, '0')} (${duration.toStringAsFixed(1)}s)'),
                Text('Quality Score: ${(score * 100).toStringAsFixed(1)}%'),
              ],
            ),
            trailing: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${(score * 100).toStringAsFixed(0)}%',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildQualityTab() {
    if (_qualityAnalysis == null) {
      return const Center(child: Text('No quality analysis available'));
    }

    final quality = _qualityAnalysis!;
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildQualityMetric('Overall Score', quality['overallScore'] ?? 0.0),
          _buildQualityMetric('Sharpness', quality['sharpness'] ?? 0.0),
          _buildQualityMetric('Brightness', quality['brightness'] ?? 0.0),
          _buildQualityMetric('Contrast', quality['contrast'] ?? 0.0),
          _buildQualityMetric('Colorfulness', quality['colorfulness'] ?? 0.0),
        ],
      ),
    );
  }

  Widget _buildQualityMetric(String label, double value) {
    final percentage = (value * 100).round();
    final color = percentage >= 80
        ? Colors.green
        : percentage >= 60
            ? Colors.orange
            : Colors.red;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
              Text('$percentage%',
                  style: TextStyle(color: color, fontWeight: FontWeight.bold)),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: value,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildMotionTab() {
    if (_motionAnalysis == null) {
      return const Center(child: Text('No motion analysis available'));
    }

    final motion = _motionAnalysis!;
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMotionMetric(
              'Average Intensity', motion['averageIntensity'] ?? 0.0),
          _buildMotionMetric('Max Intensity', motion['maxIntensity'] ?? 0.0),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoCard('Variance',
                    '${(motion['variance'] ?? 0.0).toStringAsFixed(3)}'),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildInfoCard('Events', '${motion['eventCount'] ?? 0}'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMotionMetric(String label, double value) {
    final percentage = (value * 100).round();
    final color = percentage >= 50
        ? Colors.red
        : percentage >= 25
            ? Colors.orange
            : Colors.green;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(label, style: const TextStyle(fontWeight: FontWeight.w600)),
              Text('$percentage%',
                  style: TextStyle(color: color, fontWeight: FontWeight.bold)),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: value,
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildHighlightsTab() {
    if (_highlights.isEmpty) {
      return const Center(child: Text('No highlights detected'));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _highlights.length,
      itemBuilder: (context, index) {
        final highlight = _highlights[index];
        final timestamp = highlight['timestamp'] ?? 0.0;
        final confidence = highlight['confidence'] ?? 0.0;
        final type = highlight['type'] ?? 'unknown';

        final minutes = (timestamp / 60).floor();
        final seconds = (timestamp % 60).floor();

        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            leading: Text(
              _getAnalysisEmoji(type),
              style: const TextStyle(fontSize: 24),
            ),
            title: Text(_getAnalysisDescription(type)),
            subtitle:
                Text('Time: $minutes:${seconds.toString().padLeft(2, '0')}'),
            trailing: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${(confidence * 100).toStringAsFixed(0)}%',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoCard(String label, String value) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF11998e),
            ),
          ),
          Text(
            label,
            style: const TextStyle(color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Beautiful iOS-style loader with progress and step indicators
  Widget _buildAppleStyleLoader() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Main spinner with beautiful animations
          AnimatedBuilder(
            animation: _spinnerAnimation,
            builder: (context, child) {
              return Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF38ef7d).withValues(alpha: 0.8),
                      const Color(0xFF11998e).withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    transform:
                        GradientRotation(_spinnerAnimation.value * 2 * 3.14159),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF11998e).withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Container(
                  margin: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white,
                  ),
                  child: Center(
                    child: Transform.rotate(
                      angle: _spinnerAnimation.value * 2 * 3.14159,
                      child: Icon(
                        Icons.analytics,
                        size: 32,
                        color: const Color(0xFF11998e),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 24),

          // Progress bar with smooth animation
          Container(
            width: 280,
            height: 6,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(3),
            ),
            child: AnimatedBuilder(
              animation: _progressAnimation,
              builder: (context, child) {
                return FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _analysisProgress * _progressAnimation.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Color(0xFF38ef7d), Color(0xFF11998e)],
                      ),
                      borderRadius: BorderRadius.circular(3),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xFF11998e).withValues(alpha: 0.4),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // Progress status
          Text(
            _isAnalyzing ? 'Analyzing...' : 'Complete',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF11998e),
            ),
          ),

          const SizedBox(height: 20),

          // Current step with beautiful typography
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(25),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: const Color(0xFF38ef7d),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF38ef7d).withValues(alpha: 0.5),
                        blurRadius: 8,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _currentAnalysisStep,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Step indicators
          _buildStepIndicators(),

          const SizedBox(height: 20),

          // Helpful tip
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 20),
            decoration: BoxDecoration(
              color: const Color(0xFF11998e).withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF11998e).withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb_outline,
                  color: const Color(0xFF11998e),
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Analyzing your video with advanced computer vision algorithms...',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build step indicators showing progress through analysis phases
  Widget _buildStepIndicators() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_analysisSteps.length, (index) {
        bool isCompleted = index < _currentStepIndex;
        bool isCurrent = index == _currentStepIndex;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 3),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: isCurrent ? 24 : 8,
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: isCompleted
                  ? const Color(0xFF38ef7d)
                  : isCurrent
                      ? const Color(0xFF11998e)
                      : Colors.grey[300],
              boxShadow: (isCompleted || isCurrent)
                  ? [
                      BoxShadow(
                        color: (isCompleted
                                ? const Color(0xFF38ef7d)
                                : const Color(0xFF11998e))
                            .withValues(alpha: 0.4),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : null,
            ),
          ),
        );
      }),
    );
  }
}
