import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:visionkit/visionkit.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'dart:async';

class VisionKitTab extends StatefulWidget {
  const VisionKitTab({super.key});

  @override
  VisionKitTabState createState() => VisionKitTabState();
}

class VisionKitTabState extends State<VisionKitTab> {
  bool _isAnalyzing = false;
  String _selectedFileName = '';
  String _selectedVideoPath = '';
  bool _hasSelectedFile = false;
  bool _analysisCompleted = false;
  String _analysisResult = '';
  List<VisionAnalysisResult> _comprehensiveResults = [];
  String _error = '';
  final String _analysisMode =
      'comprehensive'; // 'text', 'objects', 'comprehensive'

  @override
  void initState() {
    super.initState();
    debugPrint("VisionKit is ready.");
  }

  Future<void> _pickVideoFile() async {
    try {
      debugPrint("[VisionKitTab] Picking video file...");

      // Clear any previous errors and analysis state
      setState(() {
        _error = '';
        _analysisCompleted = false;
      });

      // Clear temporary files if possible (helps with iOS/Android)
      if (Platform.isIOS || Platform.isAndroid) {
        try {
          await FilePicker.platform.clearTemporaryFiles();
        } catch (e) {
          debugPrint('Warning: Could not clear temporary files: $e');
        }
      }

      FilePickerResult? result;
      try {
        result = await FilePicker.platform
            .pickFiles(
          type: FileType.video,
          allowMultiple: false,
          withData: false, // Don't load file data into memory
          withReadStream: false, // Don't create read stream
          allowedExtensions: null, // Let it auto-detect video types
          allowCompression: false, // Don't compress
          lockParentWindow: true, // Helps with desktop platforms
        )
            .timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            _showErrorSnackBar('File picker timed out. Please try again.');
            throw TimeoutException(
                'File picker timeout', const Duration(seconds: 30));
          },
        );
      } on TimeoutException {
        return;
      }

      if (result != null && result.files.single.path != null) {
        final pickedFile = result.files.single;

        // Check if we have a valid path
        if (pickedFile.path == null || pickedFile.path!.isEmpty) {
          _showErrorSnackBar('Could not access the selected file path.');
          return;
        }

        final file = File(pickedFile.path!);

        // Verify file exists and is accessible
        if (!await file.exists()) {
          _showErrorSnackBar(
              'Selected file is not accessible or does not exist.');
          return;
        }

        // Check file size (optional - prevent huge files)
        final fileSize = await file.length();
        final fileSizeMB = fileSize / (1024 * 1024);

        if (fileSizeMB > 500) {
          // 500MB limit
          _showErrorSnackBar(
              'File is too large (${fileSizeMB.toStringAsFixed(1)}MB). Please select a smaller video.');
          return;
        }

        final videoPath = pickedFile.path!;
        debugPrint("[VisionKitTab] Video picked: $videoPath");

        setState(() {
          _selectedFileName = pickedFile.name;
          _selectedVideoPath = videoPath;
          _hasSelectedFile = true;
          _analysisResult = '';
          _error = '';
          _analysisCompleted = false;
        });

        _showSuccessSnackBar(
            'Video file selected successfully! (${fileSizeMB.toStringAsFixed(1)}MB)');
      } else {
        debugPrint("[VisionKitTab] User cancelled file picker.");
      }
    } catch (e) {
      debugPrint("[VisionKitTab] Caught error: $e");
      String errorMessage = 'Error selecting video file: $e';

      // Provide more helpful error messages for common issues
      if (e.toString().contains('file_picker_error')) {
        errorMessage =
            'File picker error: Please make sure you have permission to access photos and files. Try going to Settings > Privacy & Security > Photos and enable access for AiClipper.';
      } else if (e.toString().contains('Failed to process any images')) {
        errorMessage =
            'Could not access the video file. Please check that the file exists and you have permission to access it.';
      }

      setState(() {
        _error = errorMessage;
      });
      _showErrorSnackBar(errorMessage);
    }
  }

  Future<void> _runVisionKitAnalysis() async {
    if (_selectedVideoPath.isEmpty) {
      _showErrorSnackBar('Please select a video file first.');
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _analysisResult = '';
      _comprehensiveResults = [];
      _error = '';
      _analysisCompleted = false;
    });

    try {
      if (_analysisMode == 'comprehensive') {
        debugPrint("[VisionKitTab] Calling analyzeVideoComprehensively...");
        final results = await VisionKit.analyzeVideoComprehensively(
          _selectedVideoPath,
          options: {
            'enableTextDetection': true,
            'enableObjectDetection': true,
            'enableSceneAnalysis': true,
          },
        );
        debugPrint(
            "[VisionKitTab] Received ${results.length} comprehensive results.");

        // Log complete results to console
        debugPrint("=== VISIONKIT COMPREHENSIVE ANALYSIS RESULTS ===");
        debugPrint("Total analysis points: ${results.length}");
        for (int i = 0; i < results.length; i++) {
          final result = results[i];
          debugPrint(
              "--- Frame ${i + 1} at ${result.timestamp.toStringAsFixed(1)}s ---");
          debugPrint(
              "Interest Score: ${(result.interestScore * 100).toInt()}%");
          debugPrint(
              "Objects (${result.objectDetections.length}): ${result.objectDetections.map((o) => o.label).join(', ')}");
          debugPrint(
              "Text (${result.textDetections.length}): ${result.textDetections.map((t) => '"${t.text}"').join(', ')}");
          if (result.sceneAnalysis != null) {
            debugPrint(
                "Scene: ${result.sceneAnalysis!.sceneType} (${(result.sceneAnalysis!.confidence * 100).toInt()}%)");
          }
          debugPrint("Metadata: ${result.metadata}");
        }
        debugPrint("=== END VISIONKIT RESULTS ===");

        setState(() {
          _comprehensiveResults = results;
          _analysisResult = _formatResults();
          _isAnalyzing = false;
          _analysisCompleted = true;
        });

        // Show success message with summary
        final highInterestCount =
            results.where((r) => r.interestScore > 0.7).length;
        final totalObjects =
            results.fold<int>(0, (sum, r) => sum + r.objectDetections.length);
        final totalText =
            results.fold<int>(0, (sum, r) => sum + r.textDetections.length);

        _showSuccessSnackBar(
            'Analysis completed! Found ${results.length} analysis points, '
            '$highInterestCount high-interest moments, $totalObjects objects, $totalText text items.');
      } else {
        // Fallback to text-only analysis
        debugPrint("[VisionKitTab] Calling detectTextInVideo...");
        final detections =
            await VisionKit.detectTextInVideo(_selectedVideoPath);
        debugPrint("[VisionKitTab] Received ${detections.length} detections.");

        // Log text detection results to console
        debugPrint("=== VISIONKIT TEXT DETECTION RESULTS ===");
        debugPrint("Total text detections: ${detections.length}");
        for (int i = 0; i < detections.length; i++) {
          final detection = detections[i];
          debugPrint(
              "--- Text ${i + 1} at ${detection.timestamp.toStringAsFixed(1)}s ---");
          debugPrint("Text: '${detection.text}'");
          debugPrint("Confidence: ${(detection.confidence * 100).toInt()}%");
          debugPrint("Bounding box: ${detection.box}");
        }
        debugPrint("=== END TEXT DETECTION RESULTS ===");

        setState(() {
          _analysisResult = _formatTextOnlyResults(detections);
          _isAnalyzing = false;
          _analysisCompleted = true;
        });

        if (detections.isEmpty) {
          _showSuccessSnackBar(
              'Analysis completed! No text was detected in this video.');
        } else {
          _showSuccessSnackBar(
              'Analysis completed! Found ${detections.length} text detection(s).');
        }
      }
    } catch (e) {
      debugPrint("[VisionKitTab] Caught error during detection: $e");
      String errorMessage = 'Error processing video: $e';

      if (e.toString().contains('DETECTION_FAILED') ||
          e.toString().contains('ANALYSIS_FAILED')) {
        errorMessage =
            'Video analysis failed. Please try with a different video file.';
      }

      setState(() {
        _error = errorMessage;
        _isAnalyzing = false;
        _analysisCompleted = false;
      });
      _showErrorSnackBar(errorMessage);
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  String _formatResults() {
    final buffer = StringBuffer();
    buffer.writeln('🎬 VisionKit Enhanced Analysis Results\n');
    buffer.writeln('📁 Video: $_selectedFileName');
    buffer.writeln('✅ Analysis Status: Completed Successfully\n');

    if (_analysisMode == 'comprehensive' && _comprehensiveResults.isNotEmpty) {
      // Format comprehensive analysis results
      buffer.writeln('🧠 AI Analysis Summary:');
      final highInterestCount =
          _comprehensiveResults.where((r) => r.interestScore > 0.7).length;
      final mediumInterestCount = _comprehensiveResults
          .where((r) => r.interestScore > 0.4 && r.interestScore <= 0.7)
          .length;
      final totalObjects = _comprehensiveResults.fold<int>(
          0, (sum, r) => sum + r.objectDetections.length);
      final totalText = _comprehensiveResults.fold<int>(
          0, (sum, r) => sum + r.textDetections.length);

      buffer.writeln('   📊 ${_comprehensiveResults.length} analysis points');
      buffer.writeln('   🌟 $highInterestCount high-interest moments (>70%)');
      buffer.writeln(
          '   ⭐ $mediumInterestCount medium-interest moments (40-70%)');
      buffer.writeln('   👥 $totalObjects objects detected');
      buffer.writeln('   📝 $totalText text items found\n');

      // Sort by interest score (highest first)
      final sortedResults =
          List<VisionAnalysisResult>.from(_comprehensiveResults);
      sortedResults.sort((a, b) => b.interestScore.compareTo(a.interestScore));

      buffer.writeln('🎯 Top Interesting Moments:\n');

      final topResults = sortedResults.take(10).toList();
      for (int i = 0; i < topResults.length; i++) {
        final result = topResults[i];
        final minutes = (result.timestamp / 60).floor();
        final seconds = (result.timestamp % 60).floor();
        final time = '$minutes:${seconds.toString().padLeft(2, '0')}';
        final scorePercent = (result.interestScore * 100).toInt();

        String scoreIcon = '🔥';
        if (result.interestScore > 0.8) {
          scoreIcon = '🔥';
        } else if (result.interestScore > 0.6) {
          scoreIcon = '⭐';
        } else if (result.interestScore > 0.4) {
          scoreIcon = '✨';
        } else {
          scoreIcon = '💫';
        }

        buffer.writeln('${i + 1}. $scoreIcon $scorePercent% Interest Score');
        buffer.writeln('   ⏱️  Time: $time');

        if (result.objectDetections.isNotEmpty) {
          final objects =
              result.objectDetections.map((o) => o.label).join(', ');
          buffer.writeln('   👥 Objects: $objects');
        }

        if (result.textDetections.isNotEmpty) {
          final texts =
              result.textDetections.map((t) => '"${t.text}"').join(', ');
          buffer.writeln('   📝 Text: $texts');
        }

        if (result.sceneAnalysis != null) {
          buffer.writeln('   🎬 Scene: ${result.sceneAnalysis!.sceneType}');
        }

        buffer.writeln('');
      }
    } else {
      buffer.writeln('🔍 Analysis Results:');
      buffer.writeln('   No significant content was detected in this video.\n');
      buffer.writeln('This could be normal for videos with:');
      buffer.writeln('• No visible objects, people, or text');
      buffer.writeln('• Low quality or very dark footage');
      buffer.writeln('• Very short duration');
      buffer.writeln('• Static scenes with minimal content\n');
      buffer.writeln(
          '💡 Try a video with people, objects, or text for better results!');
    }

    buffer.writeln('✅ Enhanced VisionKit analysis completed successfully!');
    return buffer.toString();
  }

  String _formatTextOnlyResults(List<TextDetection> detections) {
    final buffer = StringBuffer();
    buffer.writeln('🎬 VisionKit Text Detection Results\n');
    buffer.writeln('📁 Video: $_selectedFileName');
    buffer.writeln('✅ Analysis Status: Completed Successfully\n');

    if (detections.isEmpty) {
      buffer.writeln('🔍 Text Detection Results:');
      buffer.writeln('   No readable text was detected in this video.\n');
      buffer.writeln('This could be normal for videos with:');
      buffer.writeln('• No visible text or captions');
      buffer.writeln('• Low quality/blurry text');
      buffer.writeln('• Text in unsupported languages');
      buffer.writeln('• Very fast-moving text');
      buffer.writeln('• Text with low contrast\n');
      buffer.writeln(
          '💡 Try a video with clear, readable text for better results!');
    } else {
      buffer.writeln('🎯 Text Detection Results:');
      buffer.writeln('   Found ${detections.length} text detection(s)\n');

      detections.sort((a, b) => a.timestamp.compareTo(b.timestamp));

      for (int i = 0; i < detections.length; i++) {
        final detection = detections[i];
        final minutes = (detection.timestamp / 60).floor();
        final seconds = (detection.timestamp % 60).floor();
        final time = '$minutes:${seconds.toString().padLeft(2, '0')}';

        buffer.writeln('${i + 1}. "${detection.text}"');
        buffer.writeln('   ⏱️  Time: $time');
        buffer.writeln(
            '   📊 Confidence: ${(detection.confidence * 100).toStringAsFixed(1)}%\n');
      }
    }

    buffer.writeln('✅ VisionKit text analysis completed successfully!');
    return buffer.toString();
  }

  Future<void> _shareResults() async {
    if (_analysisCompleted && _analysisResult.isNotEmpty) {
      await Share.share(_analysisResult,
          subject: 'VisionKit Enhanced Analysis Results');
    }
  }

  Future<void> _copyToClipboard() async {
    if (_analysisCompleted && _analysisResult.isNotEmpty) {
      await Clipboard.setData(ClipboardData(text: _analysisResult));
      _showSuccessSnackBar('Results copied to clipboard!');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom -
                  40,
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // Header
                  _buildHeader(),
                  const SizedBox(height: 30),

                  // Main Action Area
                  _buildMainActionArea(),
                  const SizedBox(height: 30),

                  // Results Section
                  _buildResultsSection(),

                  // Error Display
                  if (_error.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 20),
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                            color: Colors.red.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.error, color: Colors.red),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _error,
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF0052D4), Color(0xFF4364F7), Color(0xFF6FB1FC)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.visibility,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          const Text(
            'VisionKit Enhanced Analysis',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            "Advanced AI analysis: objects, people, animals, text, and scene understanding.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 16,
              ),
              SizedBox(width: 8),
              Text(
                'Ready to analyze',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainActionArea() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // File Selection
          if (!_hasSelectedFile) ...[
            Icon(
              Icons.video_library,
              size: 60,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Select a video file to analyze',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'AI will analyze objects, people, animals, text, scenes, and calculate interest scores',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _pickVideoFile,
              icon: const Icon(Icons.video_library),
              label: const Text('Choose Video File'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF0052D4),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 5,
              ),
            ),
          ] else ...[
            // Selected File Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.video_file, color: Colors.green),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedFileName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        const Text(
                          'Ready for comprehensive AI analysis',
                          style: TextStyle(
                            color: Colors.green,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => setState(() {
                      _hasSelectedFile = false;
                      _analysisResult = '';
                      _error = '';
                      _analysisCompleted = false;
                    }),
                    icon: const Icon(Icons.close, color: Colors.grey),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Analyze Button
            ElevatedButton.icon(
              onPressed: _isAnalyzing ? null : _runVisionKitAnalysis,
              icon: _isAnalyzing
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                  : const Icon(Icons.visibility),
              label: Text(_isAnalyzing ? 'Analyzing...' : 'Start AI Analysis'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4364F7),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 48, vertical: 18),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 8,
              ),
            ),
            const SizedBox(height: 16),
            TextButton(
              onPressed: _pickVideoFile,
              child: const Text('Choose Different Video'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    // Show results section if analysis is completed OR currently analyzing
    if (_analysisResult.isEmpty && !_isAnalyzing) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        minHeight: 200,
        maxHeight: MediaQuery.of(context).size.height * 0.4,
      ),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Icon(_isAnalyzing ? Icons.hourglass_empty : Icons.article,
                  color: const Color(0xFF0052D4)),
              const SizedBox(width: 12),
              Text(
                _isAnalyzing ? 'Analyzing Video...' : 'Analysis Results',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_analysisResult.isNotEmpty) ...[
                IconButton(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy, size: 20),
                  tooltip: 'Copy to clipboard',
                ),
                IconButton(
                  onPressed: _shareResults,
                  icon: const Icon(Icons.share, size: 20),
                  tooltip: 'Share results',
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          Flexible(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: SingleChildScrollView(
                child: _isAnalyzing
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text(
                              'Running comprehensive AI analysis...\nDetecting objects, people, text, and scenes.\nThis may take a few moments.',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : SelectableText(
                        _analysisResult,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.6,
                          color: Colors.black87,
                        ),
                      ),
              ),
            ),
          ),
          if (_analysisResult.isNotEmpty) ...[
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: _pickVideoFile,
                  icon: const Icon(Icons.video_library),
                  label: const Text('New Video'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF0052D4),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _runVisionKitAnalysis,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Re-analyze'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4364F7),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
