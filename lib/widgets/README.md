# Widget Organization

This directory contains the organized widget components for the AiClipper application.

## Structure

### Core Tabs

- **`whisper_tab.dart`** - Whisper AI Speech Recognition functionality
- **`opencv_tab.dart`** - OpenCV Computer Vision functionality

## Widget Architecture

The application uses a tabbed interface to separate different AI functionalities:

1. **Whisper Tab** (Speech Recognition)

   - Audio/Video file selection
   - Speech-to-text transcription
   - Multilingual support
   - Model testing functionality

2. **OpenCV Tab** (Computer Vision)
   - Video file selection
   - Computer vision analysis
   - Highlight detection
   - Plugin integration testing

## Key Features

### Isolation

- Each tab operates independently
- No cross-contamination of state
- Modular and maintainable code structure

### Testing

- Built-in plugin testing functionality
- Clear error reporting and debugging
- Step-by-step verification process

### File Management

- Consistent file picker interfaces
- Clear file information display
- Proper error handling

## Usage

Import and use in main application:

```dart
import 'widgets/whisper_tab.dart';
import 'widgets/opencv_tab.dart';

// Use in TabBarView
TabBarView(
  children: [
    WhisperTab(),
    OpenCVTab(),
  ],
)
```

## Development Notes

- All dependencies are properly imported in each widget
- State management is encapsulated within each tab
- Error handling provides clear debugging information
- UI is consistent across both tabs
