import 'package:flutter/material.dart';
import '../services/opencv_video_analysis_service_ffi.dart';

class OpenCVDetailedTestWidget extends StatefulWidget {
  const OpenCVDetailedTestWidget({super.key});

  @override
  State<OpenCVDetailedTestWidget> createState() =>
      _OpenCVDetailedTestWidgetState();
}

class _OpenCVDetailedTestWidgetState extends State<OpenCVDetailedTestWidget> {
  String _status = 'Ready';
  Map<String, dynamic>? _detailedResults;
  bool _isAnalyzing = false;

  @override
  void initState() {
    super.initState();
    _initializeOpenCV();
  }

  Future<void> _initializeOpenCV() async {
    setState(() {
      _status = 'Initializing OpenCV...';
    });

    try {
      final success = await OpenCVVideoAnalysisService.initialize();
      if (success) {
        final version = OpenCVVideoAnalysisService.getVersion();
        setState(() {
          _status = 'OpenCV initialized: $version';
        });
      } else {
        setState(() {
          _status = 'OpenCV initialization failed';
        });
      }
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
    }
  }

  Future<void> _runDetailedAnalysis() async {
    if (_isAnalyzing) return;

    setState(() {
      _isAnalyzing = true;
      _status = 'Running detailed analysis...';
      _detailedResults = null;
    });

    try {
      // Use a sample video path - in real app this would come from file picker
      const sampleVideoPath = '/path/to/sample/video.mp4';

      final results = await OpenCVVideoAnalysisService.performDetailedAnalysis(
        sampleVideoPath,
        startTime: 0.0,
        duration: 10.0,
      );

      setState(() {
        _detailedResults = results;
        _status = 'Analysis complete';
        _isAnalyzing = false;
      });
    } catch (e) {
      setState(() {
        _status = 'Analysis failed: $e';
        _isAnalyzing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('OpenCV Detailed Analysis Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(_status),
                    if (_isAnalyzing) ...[
                      const SizedBox(height: 8),
                      const LinearProgressIndicator(),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Controls
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Controls',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: _isAnalyzing ? null : _runDetailedAnalysis,
                          child: const Text('Run Detailed Analysis'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: _isAnalyzing ? null : _initializeOpenCV,
                          child: const Text('Reinitialize'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Results
            if (_detailedResults != null) ...[
              Expanded(
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Detailed Analysis Results',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 16),
                        Expanded(
                          child: SingleChildScrollView(
                            child: _buildResultsDisplay(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultsDisplay() {
    if (_detailedResults == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Motion Analysis
        if (_detailedResults!['motion_detailed'] != null) ...[
          _buildSectionHeader('Motion Analysis'),
          _buildMetricsDisplay('Basic Motion Score',
              _detailedResults!['motion_basic']?.toString() ?? 'N/A'),
          ..._buildDetailedMetrics(
              'Motion Details', _detailedResults!['motion_detailed']),
          const SizedBox(height: 16),
        ],

        // Quality Analysis
        if (_detailedResults!['quality_detailed'] != null) ...[
          _buildSectionHeader('Quality Analysis'),
          _buildMetricsDisplay('Basic Quality Score',
              _detailedResults!['quality_basic']?.toString() ?? 'N/A'),
          ..._buildDetailedMetrics(
              'Quality Details', _detailedResults!['quality_detailed']),
          const SizedBox(height: 16),
        ],

        // Face Analysis
        if (_detailedResults!['faces_detailed'] != null) ...[
          _buildSectionHeader('Face Analysis'),
          _buildMetricsDisplay('Basic Face Count',
              _detailedResults!['faces_basic']?.toString() ?? 'N/A'),
          ..._buildDetailedMetrics(
              'Face Details', _detailedResults!['faces_detailed']),
          const SizedBox(height: 16),
        ],

        // Highlights
        if (_detailedResults!['highlights'] != null) ...[
          _buildSectionHeader('Video Highlights'),
          _buildHighlightsDisplay(_detailedResults!['highlights']),
        ],
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildMetricsDisplay(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          SizedBox(
            width: 200,
            child: Text(label,
                style: const TextStyle(fontWeight: FontWeight.w500)),
          ),
          Text(value),
        ],
      ),
    );
  }

  List<Widget> _buildDetailedMetrics(
      String title, Map<String, dynamic>? metrics) {
    if (metrics == null) return [];

    return [
      Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
      const SizedBox(height: 4),
      ...metrics.entries.map(
          (entry) => _buildMetricsDisplay(entry.key, entry.value.toString())),
    ];
  }

  Widget _buildHighlightsDisplay(List<dynamic>? highlights) {
    if (highlights == null || highlights.isEmpty) {
      return const Text('No highlights found');
    }

    return Column(
      children: highlights.map<Widget>((highlight) {
        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4.0),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                    '${highlight['timestamp']?.toStringAsFixed(1)}s - ${highlight['type']}',
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                Text('Duration: ${highlight['duration']?.toStringAsFixed(1)}s'),
                Text(
                    'Confidence: ${highlight['confidence']?.toStringAsFixed(2)}'),
                Text('Reason: ${highlight['extractionReason']}'),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }
}
