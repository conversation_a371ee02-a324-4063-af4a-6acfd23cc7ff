import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:mediapipe/mediapipe.dart';
import 'package:visionkit/visionkit.dart';
import 'package:aiclipper_whisper/aiclipper_whisper.dart';
import 'dart:convert';

/// Example widget showing how to combine MediaPipe, VisionKit, and Whisper
/// for comprehensive video analysis as described in the MediaPipe prompt.
class CombinedAnalysisExample extends StatefulWidget {
  const CombinedAnalysisExample({super.key});

  @override
  State<CombinedAnalysisExample> createState() =>
      _CombinedAnalysisExampleState();
}

class _CombinedAnalysisExampleState extends State<CombinedAnalysisExample> {
  String _selectedVideoPath = '';
  bool _isAnalyzing = false;
  List<ClipFrameData> _clipFrames = [];

  Future<void> _runCombinedAnalysis() async {
    if (_selectedVideoPath.isEmpty) return;

    setState(() {
      _isAnalyzing = true;
      _clipFrames = [];
    });

    try {
      // Step 1: Run MediaPipe analysis
      print('🤖 Running MediaPipe analysis...');
      final mediaPipeResults = await MediaPipe.analyzeVideo(
        _selectedVideoPath,
        enablePoseDetection: true,
        enableHandDetection: true,
        enableFaceDetection: true,
        enableObjectDetection: true,
        confidenceThreshold: 0.5,
        maxResults: 5,
      );

      // Step 2: Run VisionKit analysis
      print('👁️ Running VisionKit analysis...');
      final visionKitResults = await VisionKit.analyzeVideoComprehensively(
        _selectedVideoPath,
        options: {
          'enableTextDetection': true,
          'enableBasicFaceDetection': true,
          'enableBasicBodyPose': true,
          'enableVisualMetrics': true,
          'frameAnalysisInterval': 1.0,
        },
      );

      // Step 3: Run Whisper transcription
      print('🎤 Running Whisper transcription...');
      final whisperResult = await AiclipperWhisper.transcribeAudio(
        modelPath: 'assets/models/ggml-base.en.bin',
        audioPath: _selectedVideoPath,
      );
      final transcriptionSegments = _parseWhisperResult(whisperResult);

      // Step 4: Combine all results into clip scoring data
      print('🔄 Combining analysis results...');
      final combinedFrames = _combineAnalysisResults(
        mediaPipeResults,
        visionKitResults,
        transcriptionSegments,
      );

      // Step 5: Score and rank clips
      final scoredClips = _scoreClips(combinedFrames);

      setState(() {
        _clipFrames = scoredClips;
        _isAnalyzing = false;
      });

      print(
          '✅ Combined analysis complete! Found ${scoredClips.length} scored frames.');
    } catch (e) {
      print('❌ Combined analysis error: $e');
      setState(() => _isAnalyzing = false);
    }
  }

  List<TranscriptionSegment> _parseWhisperResult(String whisperJson) {
    try {
      final data = jsonDecode(whisperJson);
      final segments = data['segments'] as List? ?? [];
      return segments
          .map((seg) => TranscriptionSegment(
                text: seg['text'] ?? '',
                startTime: (seg['start'] as num?)?.toDouble() ?? 0.0,
                endTime: (seg['end'] as num?)?.toDouble() ?? 0.0,
              ))
          .toList();
    } catch (e) {
      print('Error parsing Whisper result: $e');
      return [];
    }
  }

  List<ClipFrameData> _combineAnalysisResults(
    List<MediaPipeResult> mediaPipeResults,
    List<VisionAnalysisResult> visionKitResults,
    List<TranscriptionSegment> transcriptionSegments,
  ) {
    final Map<double, ClipFrameData> frameMap = {};

    // Process MediaPipe results
    for (final mpResult in mediaPipeResults) {
      final timestamp = mpResult.timestamp.millisecondsSinceEpoch / 1000.0;

      frameMap[timestamp] = ClipFrameData(
        timestamp: timestamp,
        interestScore: 0.0, // Will be calculated later
        transcription: _getTranscriptionAt(timestamp, transcriptionSegments),
        poseConfidence: mpResult.pose?.confidence ?? 0.0,
        faceConfidence: mpResult.face?.confidence ?? 0.0,
        objectLabels: mpResult.objects.map((o) => o.label).toList(),
        handCount: mpResult.hands.length,
        objectCount: mpResult.objects.length,
        mediaPipeData: mpResult,
      );
    }

    // Merge with VisionKit results
    for (final vkResult in visionKitResults) {
      final timestamp = vkResult.timestamp;

      if (frameMap.containsKey(timestamp)) {
        frameMap[timestamp] = frameMap[timestamp]!.copyWith(
          interestScore: vkResult.interestScore,
          visionKitData: vkResult,
        );
      } else {
        frameMap[timestamp] = ClipFrameData(
          timestamp: timestamp,
          interestScore: vkResult.interestScore,
          transcription: _getTranscriptionAt(timestamp, transcriptionSegments),
          poseConfidence: 0.0,
          faceConfidence: 0.0,
          objectLabels: [],
          handCount: 0,
          objectCount: 0,
          visionKitData: vkResult,
        );
      }
    }

    return frameMap.values.toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  String _getTranscriptionAt(
      double timestamp, List<TranscriptionSegment> segments) {
    for (final segment in segments) {
      if (timestamp >= segment.startTime && timestamp <= segment.endTime) {
        return segment.text;
      }
    }
    return '';
  }

  List<ClipFrameData> _scoreClips(List<ClipFrameData> frames) {
    // Enhanced scoring algorithm using all available data
    for (int i = 0; i < frames.length; i++) {
      final frame = frames[i];
      double score = frame.interestScore; // Base VisionKit score

      // MediaPipe enhancements
      if (frame.poseConfidence > 0.7) score += 0.2; // Strong pose detection
      if (frame.faceConfidence > 0.7) score += 0.15; // Strong face detection
      if (frame.handCount > 0) score += 0.1 * frame.handCount; // Hand gestures
      if (frame.objectCount > 2) score += 0.1; // Multiple objects

      // Transcription enhancements
      if (frame.transcription.isNotEmpty) {
        score += 0.1; // Has speech
        if (_isExcitingTranscription(frame.transcription)) {
          score += 0.2; // Exciting content
        }
      }

      // Temporal context (momentum)
      if (i > 0 && i < frames.length - 1) {
        final prevScore = frames[i - 1].interestScore;
        final nextScore = frames[i + 1].interestScore;
        final momentum = (score + nextScore) - prevScore;
        if (momentum > 0.3) score += 0.1; // Building momentum
      }

      frames[i] = frame.copyWith(combinedScore: score);
    }

    // Sort by combined score (highest first)
    frames.sort(
        (a, b) => (b.combinedScore ?? 0.0).compareTo(a.combinedScore ?? 0.0));
    return frames;
  }

  bool _isExcitingTranscription(String text) {
    final excitingWords = [
      'wow',
      'amazing',
      'incredible',
      'awesome',
      'fantastic',
      'great',
      'excellent'
    ];
    final lowerText = text.toLowerCase();
    return excitingWords.any((word) => lowerText.contains(word));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Combined Analysis Example'),
        backgroundColor: Colors.purple,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Header
            const Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(Icons.integration_instructions,
                        size: 48, color: Colors.purple),
                    SizedBox(height: 8),
                    Text(
                      'Combined Analysis Demo',
                      style:
                          TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'MediaPipe + VisionKit + Whisper integration for smart highlight detection',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // File selection and analysis
            ElevatedButton.icon(
              onPressed: _isAnalyzing
                  ? null
                  : () async {
                      final result = await FilePicker.platform
                          .pickFiles(type: FileType.video);
                      if (result?.files.single.path != null) {
                        setState(() {
                          _selectedVideoPath = result!.files.single.path!;
                        });
                      }
                    },
              icon: const Icon(Icons.video_file),
              label: Text(_selectedVideoPath.isEmpty
                  ? 'Select Video'
                  : 'Video Selected'),
            ),

            const SizedBox(height: 16),

            if (_selectedVideoPath.isNotEmpty) ...[
              ElevatedButton.icon(
                onPressed: _isAnalyzing ? null : _runCombinedAnalysis,
                icon: _isAnalyzing
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.analytics),
                label: Text(
                    _isAnalyzing ? 'Analyzing...' : 'Run Combined Analysis'),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
              ),
            ],

            const SizedBox(height: 16),

            // Results
            if (_clipFrames.isNotEmpty) ...[
              const Text(
                'Top Highlight Moments',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: _clipFrames.take(10).length,
                  itemBuilder: (context, index) {
                    final frame = _clipFrames[index];
                    return Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor:
                              _getScoreColor(frame.combinedScore ?? 0.0),
                          child: Text('${index + 1}'),
                        ),
                        title: Text('${frame.timestamp.toStringAsFixed(1)}s'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                                'Score: ${(frame.combinedScore ?? 0.0).toStringAsFixed(2)}'),
                            if (frame.transcription.isNotEmpty)
                              Text('Speech: "${frame.transcription}"'),
                            Text(
                                'Pose: ${frame.poseConfidence.toStringAsFixed(2)}, '
                                'Face: ${frame.faceConfidence.toStringAsFixed(2)}, '
                                'Objects: ${frame.objectCount}'),
                          ],
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
              ),
            ] else if (_isAnalyzing) ...[
              const Center(
                child: Column(
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Running combined analysis...'),
                    Text('This may take several minutes.'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getScoreColor(double score) {
    if (score > 0.8) return Colors.green;
    if (score > 0.6) return Colors.orange;
    return Colors.red;
  }
}

/// Data class combining all analysis results for a single frame
class ClipFrameData {
  final double timestamp;
  final double interestScore;
  final String transcription;
  final double poseConfidence;
  final double faceConfidence;
  final List<String> objectLabels;
  final int handCount;
  final int objectCount;
  final double? combinedScore;
  final MediaPipeResult? mediaPipeData;
  final VisionAnalysisResult? visionKitData;

  ClipFrameData({
    required this.timestamp,
    required this.interestScore,
    required this.transcription,
    required this.poseConfidence,
    required this.faceConfidence,
    required this.objectLabels,
    required this.handCount,
    required this.objectCount,
    this.combinedScore,
    this.mediaPipeData,
    this.visionKitData,
  });

  ClipFrameData copyWith({
    double? timestamp,
    double? interestScore,
    String? transcription,
    double? poseConfidence,
    double? faceConfidence,
    List<String>? objectLabels,
    int? handCount,
    int? objectCount,
    double? combinedScore,
    MediaPipeResult? mediaPipeData,
    VisionAnalysisResult? visionKitData,
  }) {
    return ClipFrameData(
      timestamp: timestamp ?? this.timestamp,
      interestScore: interestScore ?? this.interestScore,
      transcription: transcription ?? this.transcription,
      poseConfidence: poseConfidence ?? this.poseConfidence,
      faceConfidence: faceConfidence ?? this.faceConfidence,
      objectLabels: objectLabels ?? this.objectLabels,
      handCount: handCount ?? this.handCount,
      objectCount: objectCount ?? this.objectCount,
      combinedScore: combinedScore ?? this.combinedScore,
      mediaPipeData: mediaPipeData ?? this.mediaPipeData,
      visionKitData: visionKitData ?? this.visionKitData,
    );
  }
}

/// Simple transcription segment data
class TranscriptionSegment {
  final String text;
  final double startTime;
  final double endTime;

  TranscriptionSegment({
    required this.text,
    required this.startTime,
    required this.endTime,
  });
}
