import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:mediapipe/mediapipe.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';

class MediaPipeTab extends StatefulWidget {
  const MediaPipeTab({super.key});

  @override
  MediaPipeTabState createState() => MediaPipeTabState();
}

class MediaPipeTabState extends State<MediaPipeTab> {
  bool _isAnalyzing = false;
  String _selectedFileName = '';
  String _selectedVideoPath = '';
  bool _hasSelectedFile = false;
  bool _analysisCompleted = false;
  String _analysisResult = '';
  List<MediaPipeResult> _mediaPipeResults = [];
  String _error = '';

  // Real-time progress tracking
  double _analysisProgress = 0.0;
  String _analysisStatusMessage = '';
  StreamSubscription? _progressSubscription;

  // MediaPipe analysis type flags
  bool _enablePoseDetection = true;
  bool _enableHandDetection = true;
  bool _enableFaceDetection = true;
  bool _enableObjectDetection = true;

  // Configuration
  double _confidenceThreshold = 0.5;
  int _maxResults = 5;

  @override
  void initState() {
    super.initState();
    debugPrint("MediaPipe is ready.");

    // Set up progress stream listener
    _setupProgressListener();
  }

  void _setupProgressListener() {
    const progressChannel = EventChannel('mediapipe/results');
    _progressSubscription =
        progressChannel.receiveBroadcastStream().listen((data) {
      debugPrint(
          "${DateTime.now()}: [MediaPipeTab] Progress data received: $data");
      if (data is Map) {
        setState(() {
          _analysisProgress = data['progress'] ?? 0.0;
          _analysisStatusMessage = data['statusMessage'] ?? '';
        });
      }
    }, onError: (error) {
      debugPrint(
          "${DateTime.now()}: [MediaPipeTab] Progress stream error: $error");
    }, onDone: () {
      debugPrint("${DateTime.now()}: [MediaPipeTab] Progress stream is done.");
    }, cancelOnError: false);
    debugPrint(
        "${DateTime.now()}: [MediaPipeTab] Progress stream listener set up.");
  }

  @override
  void dispose() {
    _progressSubscription?.cancel();
    super.dispose();
  }

  Future<void> _pickVideoFile() async {
    try {
      debugPrint("${DateTime.now()}: [MediaPipeTab] Picking video file...");

      // Clear any previous errors and analysis state
      setState(() {
        _error = '';
        _analysisCompleted = false;
        _analysisProgress = 0.0;
        _analysisStatusMessage = 'Initializing analysis...';
      });

      // Clear temporary files if possible
      if (Platform.isIOS || Platform.isAndroid) {
        try {
          await FilePicker.platform.clearTemporaryFiles();
        } catch (e) {
          debugPrint(
              '${DateTime.now()}: Warning: Could not clear temporary files: $e');
        }
      }

      FilePickerResult? result;
      try {
        result = await FilePicker.platform
            .pickFiles(
          type: FileType.video,
          allowMultiple: false,
          withData: false,
          withReadStream: false,
          allowedExtensions: null,
          allowCompression: false,
          lockParentWindow: true,
        )
            .timeout(
          const Duration(seconds: 30),
          onTimeout: () {
            _showErrorSnackBar('File picker timed out. Please try again.');
            throw TimeoutException(
                'File picker timeout', const Duration(seconds: 30));
          },
        );
      } on TimeoutException {
        return;
      }

      if (result != null && result.files.single.path != null) {
        final pickedFile = result.files.single;

        if (pickedFile.path == null || pickedFile.path!.isEmpty) {
          _showErrorSnackBar('Could not access the selected file path.');
          return;
        }

        final file = File(pickedFile.path!);

        if (!await file.exists()) {
          _showErrorSnackBar(
              'Selected file is not accessible or does not exist.');
          return;
        }

        // Check file size
        final fileSize = await file.length();
        final fileSizeMB = fileSize / (1024 * 1024);

        if (fileSizeMB > 500) {
          _showErrorSnackBar(
              'File is too large (${fileSizeMB.toStringAsFixed(1)}MB). Please select a smaller video.');
          return;
        }

        final videoPath = pickedFile.path!;
        debugPrint(
            "${DateTime.now()}: [MediaPipeTab] Video picked: $videoPath");

        setState(() {
          _selectedFileName = pickedFile.name;
          _selectedVideoPath = videoPath;
          _hasSelectedFile = true;
          _analysisResult = '';
          _error = '';
          _analysisCompleted = false;
        });

        _showSuccessSnackBar(
            'Video file selected successfully! (${fileSizeMB.toStringAsFixed(1)}MB)');
      } else {
        debugPrint(
            "${DateTime.now()}: [MediaPipeTab] User cancelled file picker.");
      }
    } catch (e) {
      debugPrint("${DateTime.now()}: [MediaPipeTab] Caught error: $e");
      String errorMessage = 'Error selecting video file: $e';

      if (e.toString().contains('file_picker_error')) {
        errorMessage =
            'File picker error: Please make sure you have permission to access photos and files.';
      }

      setState(() {
        _error = errorMessage;
      });
      _showErrorSnackBar(errorMessage);
    }
  }

  Future<void> _runMediaPipeAnalysis() async {
    if (_selectedVideoPath.isEmpty) {
      _showErrorSnackBar('Please select a video file first.');
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _analysisResult = '';
      _mediaPipeResults = [];
      _error = '';
      _analysisCompleted = false;
      _analysisProgress = 0.0;
      _analysisStatusMessage = 'Initializing MediaPipe analysis...';
    });

    try {
      // Check if any analysis type is enabled
      if (!_enablePoseDetection &&
          !_enableHandDetection &&
          !_enableFaceDetection &&
          !_enableObjectDetection) {
        _showErrorSnackBar('Please enable at least one detection type.');
        setState(() {
          _isAnalyzing = false;
        });
        return;
      }

      // Analyze video with MediaPipe
      final results = await MediaPipe.analyzeVideo(
        _selectedVideoPath,
        enablePoseDetection: _enablePoseDetection,
        enableHandDetection: _enableHandDetection,
        enableFaceDetection: _enableFaceDetection,
        enableObjectDetection: _enableObjectDetection,
        confidenceThreshold: _confidenceThreshold,
        maxResults: _maxResults,
      );

      setState(() {
        _mediaPipeResults = results;
        _analysisResult = _formatResults();
        _isAnalyzing = false;
        _analysisCompleted = true;
      });

      // Show success message with summary
      final poseCount = results.where((r) => r.pose != null).length;
      final handCount = results.where((r) => r.hands.isNotEmpty).length;
      final faceCount = results.where((r) => r.face != null).length;
      final objectCount = results.where((r) => r.objects.isNotEmpty).length;

      _showSuccessSnackBar(
          'MediaPipe analysis completed! Found ${results.length} analysis points: '
          '$poseCount poses, $handCount hands, $faceCount faces, $objectCount objects.');
    } on Exception catch (e) {
      setState(() {
        _isAnalyzing = false;
        _analysisCompleted = false;
      });

      String errorMessage = 'MediaPipe analysis failed: ${e.toString()}';

      if (e.toString().contains('timeout')) {
        errorMessage +=
            '\n\nTry:\n• Using a shorter video\n• Reducing confidence threshold\n• Disabling some detection types';
      } else if (e.toString().contains('not playable')) {
        errorMessage +=
            '\n\nTry:\n• Using a different video format (MP4, MOV)\n• Checking if the video file is corrupted';
      }

      _showErrorSnackBar(errorMessage);
      print('MediaPipe analysis error: $e');
    }
  }

  String _formatResults() {
    final buffer = StringBuffer();
    buffer.writeln('🤖 MediaPipe Analysis Results\n');
    buffer.writeln('📁 Video: $_selectedFileName');
    buffer.writeln('✅ Analysis Status: Completed Successfully\n');

    if (_mediaPipeResults.isNotEmpty) {
      buffer.writeln('🧠 MediaPipe Detection Summary:');

      final poseDetections =
          _mediaPipeResults.where((r) => r.pose != null).length;
      final handDetections =
          _mediaPipeResults.where((r) => r.hands.isNotEmpty).length;
      final faceDetections =
          _mediaPipeResults.where((r) => r.face != null).length;
      final objectDetections =
          _mediaPipeResults.where((r) => r.objects.isNotEmpty).length;

      final totalPoses = _mediaPipeResults.where((r) => r.pose != null).length;
      final totalHands =
          _mediaPipeResults.fold<int>(0, (sum, r) => sum + r.hands.length);
      final totalFaces = _mediaPipeResults.where((r) => r.face != null).length;
      final totalObjects =
          _mediaPipeResults.fold<int>(0, (sum, r) => sum + r.objects.length);

      buffer.writeln('   📊 ${_mediaPipeResults.length} total analysis frames');
      buffer.writeln('   🤸 $poseDetections frames with pose detection');
      buffer.writeln('   👋 $handDetections frames with hand detection');
      buffer.writeln('   😊 $faceDetections frames with face detection');
      buffer.writeln('   🎯 $objectDetections frames with object detection');
      buffer.writeln('');
      buffer.writeln('📈 Detailed Counts:');
      buffer.writeln('   • Total poses detected: $totalPoses');
      buffer.writeln('   • Total hands detected: $totalHands');
      buffer.writeln('   • Total faces detected: $totalFaces');
      buffer.writeln('   • Total objects detected: $totalObjects');
      buffer.writeln('');

      // Show some example detections
      if (_enablePoseDetection && totalPoses > 0) {
        buffer.writeln('🤸 Pose Detection Examples:');
        var poseExamples =
            _mediaPipeResults.where((r) => r.pose != null).take(3);
        for (var result in poseExamples) {
          final timeStr = (result.timestamp.millisecondsSinceEpoch / 1000.0)
              .toStringAsFixed(1);
          buffer.writeln(
              '   • ${timeStr}s: ${result.pose!.landmarks.length} landmarks detected (confidence: ${result.pose!.confidence.toStringAsFixed(2)})');
        }
        buffer.writeln('');
      }

      if (_enableHandDetection && totalHands > 0) {
        buffer.writeln('👋 Hand Detection Examples:');
        var handExamples =
            _mediaPipeResults.where((r) => r.hands.isNotEmpty).take(3);
        for (var result in handExamples) {
          final timeStr = (result.timestamp.millisecondsSinceEpoch / 1000.0)
              .toStringAsFixed(1);
          for (var hand in result.hands.take(2)) {
            buffer.writeln(
                '   • ${timeStr}s: ${hand.handedness} hand, ${hand.landmarks.length} landmarks (confidence: ${hand.confidence.toStringAsFixed(2)})');
          }
        }
        buffer.writeln('');
      }

      if (_enableFaceDetection && totalFaces > 0) {
        buffer.writeln('😊 Face Detection Examples:');
        var faceExamples =
            _mediaPipeResults.where((r) => r.face != null).take(3);
        for (var result in faceExamples) {
          final timeStr = (result.timestamp.millisecondsSinceEpoch / 1000.0)
              .toStringAsFixed(1);
          buffer.writeln(
              '   • ${timeStr}s: Face with ${result.face!.landmarks.length} landmarks (confidence: ${result.face!.confidence.toStringAsFixed(2)})');
        }
        buffer.writeln('');
      }

      if (_enableObjectDetection && totalObjects > 0) {
        buffer.writeln('🎯 Object Detection Examples:');
        var objectExamples =
            _mediaPipeResults.where((r) => r.objects.isNotEmpty).take(3);
        for (var result in objectExamples) {
          final timeStr = (result.timestamp.millisecondsSinceEpoch / 1000.0)
              .toStringAsFixed(1);
          for (var obj in result.objects.take(2)) {
            buffer.writeln(
                '   • ${timeStr}s: ${obj.label} (confidence: ${obj.confidence.toStringAsFixed(2)})');
          }
        }
        buffer.writeln('');
      }
    } else {
      buffer.writeln('🔍 Analysis Results:');
      buffer.writeln('   No MediaPipe detections found in this video.\n');
      buffer.writeln('This could be normal for videos with:');
      buffer.writeln('• No visible people, hands, or faces');
      buffer.writeln('• Low quality or very dark footage');
      buffer.writeln('• Very short duration');
      buffer.writeln('• Static scenes with minimal movement\n');
      buffer.writeln(
          '💡 Try adjusting confidence threshold or using a different video!');
    }

    buffer.writeln('✅ MediaPipe analysis completed successfully!');
    return buffer.toString();
  }

  Future<void> _shareResultsAsJson() async {
    if (!_analysisCompleted || _mediaPipeResults.isEmpty) {
      _showErrorSnackBar('No analysis results to save.');
      return;
    }

    try {
      final directory = await getTemporaryDirectory();
      final baseName = _selectedFileName.isNotEmpty
          ? _selectedFileName.replaceAll(RegExp(r'\.[^.]+$'), '')
          : 'analysis';
      final fileName =
          '$baseName-mediapipe-${DateTime.now().millisecondsSinceEpoch}.json';
      final filePath = '${directory.path}/$fileName';
      final file = File(filePath);

      final resultsAsMaps = _mediaPipeResults.map((r) => r.toMap()).toList();
      const encoder = JsonEncoder.withIndent('  ');
      final jsonString = encoder.convert(resultsAsMaps);

      await file.writeAsString(jsonString);

      await Share.shareXFiles(
        [XFile(filePath)],
        subject: 'MediaPipe Analysis: $_selectedFileName',
        text:
            'Here is the detailed MediaPipe JSON analysis for $_selectedFileName.',
      );

      debugPrint('Successfully shared MediaPipe JSON file from $filePath');
    } catch (e) {
      _showErrorSnackBar('Failed to share JSON results: $e');
      debugPrint('Error sharing JSON: $e');
    }
  }

  Future<void> _shareResults() async {
    if (_analysisCompleted && _analysisResult.isNotEmpty) {
      await Share.share(_analysisResult, subject: 'MediaPipe Analysis Results');
    }
  }

  Future<void> _copyToClipboard() async {
    if (_analysisCompleted && _analysisResult.isNotEmpty) {
      await Clipboard.setData(ClipboardData(text: _analysisResult));
      _showSuccessSnackBar('Results copied to clipboard!');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            children: [
              // Header
              _buildHeader(),
              const SizedBox(height: 30),

              // Main Action Area
              _buildMainActionArea(),
              const SizedBox(height: 30),

              // Results Section
              _buildResultsSection(),

              // Error Display
              if (_error.isNotEmpty)
                Container(
                  margin: const EdgeInsets.only(top: 20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border:
                        Border.all(color: Colors.red.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.error, color: Colors.red),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _error,
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4A90E2), Color(0xFF50E3C2), Color(0xFF7ED321)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.gesture,
            size: 50,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          const Text(
            'MediaPipe Analysis',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            "Advanced pose, hand, face, and object detection using Google's MediaPipe framework.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 16),
              SizedBox(width: 8),
              Text(
                'Ready to analyze',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainActionArea() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // File Selection
          if (!_hasSelectedFile) ...[
            Icon(
              Icons.video_library,
              size: 60,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Select a video file to analyze',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'MediaPipe will detect poses, hands, faces, and objects in your video',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _pickVideoFile,
              icon: const Icon(Icons.video_library),
              label: const Text('Choose Video File'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4A90E2),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 5,
              ),
            ),
          ] else ...[
            // File Selected
            _buildFileInfo(),
            const SizedBox(height: 24),
            _buildAnalysisControls(),
            const SizedBox(height: 24),
            _buildAnalysisButton(),
          ],
        ],
      ),
    );
  }

  Widget _buildFileInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF4A90E2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.video_file, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Video Selected',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4A90E2),
                  ),
                ),
                Text(
                  _selectedFileName,
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _hasSelectedFile = false;
                _selectedFileName = '';
                _selectedVideoPath = '';
                _analysisCompleted = false;
                _analysisResult = '';
                _error = '';
              });
            },
            icon: const Icon(Icons.close, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisControls() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Detection Types',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF4A90E2),
          ),
        ),
        const SizedBox(height: 16),

        // Detection type checkboxes
        _buildDetectionCheckbox(
          'Pose Detection',
          'Detect human body poses and landmarks',
          _enablePoseDetection,
          Icons.accessibility,
          (value) => setState(() => _enablePoseDetection = value),
        ),
        _buildDetectionCheckbox(
          'Hand Detection',
          'Detect hand poses and finger landmarks',
          _enableHandDetection,
          Icons.pan_tool,
          (value) => setState(() => _enableHandDetection = value),
        ),
        _buildDetectionCheckbox(
          'Face Detection',
          'Detect facial landmarks and expressions',
          _enableFaceDetection,
          Icons.face,
          (value) => setState(() => _enableFaceDetection = value),
        ),
        _buildDetectionCheckbox(
          'Object Detection',
          'Detect and classify objects in the scene',
          _enableObjectDetection,
          Icons.search,
          (value) => setState(() => _enableObjectDetection = value),
        ),

        const SizedBox(height: 20),

        // Configuration sliders
        const Text(
          'Configuration',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF4A90E2),
          ),
        ),
        const SizedBox(height: 16),

        // Confidence threshold
        Text(
          'Confidence Threshold: ${_confidenceThreshold.toStringAsFixed(2)}',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        Slider(
          value: _confidenceThreshold,
          min: 0.1,
          max: 1.0,
          divisions: 9,
          onChanged: (value) => setState(() => _confidenceThreshold = value),
          activeColor: const Color(0xFF4A90E2),
        ),

        const SizedBox(height: 12),

        // Max results
        Text(
          'Max Results: $_maxResults',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        Slider(
          value: _maxResults.toDouble(),
          min: 1,
          max: 10,
          divisions: 9,
          onChanged: (value) => setState(() => _maxResults = value.toInt()),
          activeColor: const Color(0xFF4A90E2),
        ),
      ],
    );
  }

  Widget _buildDetectionCheckbox(
    String title,
    String description,
    bool value,
    IconData icon,
    Function(bool) onChanged,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: CheckboxListTile(
        title: Row(
          children: [
            Icon(icon, size: 20, color: const Color(0xFF4A90E2)),
            const SizedBox(width: 8),
            Text(title, style: const TextStyle(fontWeight: FontWeight.w500)),
          ],
        ),
        subtitle: Text(description, style: const TextStyle(fontSize: 12)),
        value: value,
        onChanged: (bool? newValue) => onChanged(newValue ?? false),
        activeColor: const Color(0xFF4A90E2),
        contentPadding: const EdgeInsets.all(0),
      ),
    );
  }

  Widget _buildAnalysisButton() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _selectedVideoPath.isEmpty || _isAnalyzing
                ? null
                : _runMediaPipeAnalysis,
            icon: _isAnalyzing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Icon(Icons.gesture),
            label: Text(
                _isAnalyzing ? 'Analyzing...' : 'Start MediaPipe Analysis'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4A90E2),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 48, vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
              elevation: 8,
            ),
          ),
        ),
        if (_isAnalyzing) ...[
          const SizedBox(height: 20),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                LinearProgressIndicator(
                  value: _analysisProgress,
                  backgroundColor: Colors.blue.withValues(alpha: 0.3),
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
                ),
                const SizedBox(height: 12),
                Text(
                  _analysisStatusMessage.isNotEmpty
                      ? _analysisStatusMessage
                      : 'Running MediaPipe analysis...',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF4A90E2),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildResultsSection() {
    if (!_analysisCompleted) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF4A90E2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child:
                    const Icon(Icons.analytics, color: Colors.white, size: 24),
              ),
              const SizedBox(width: 16),
              const Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Analysis Results',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF4A90E2),
                      ),
                    ),
                    Text(
                      'MediaPipe analysis completed successfully',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Results text
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: SingleChildScrollView(
              child: Text(
                _analysisResult,
                style: const TextStyle(
                  fontSize: 12,
                  fontFamily: 'monospace',
                  height: 1.4,
                ),
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareResults,
                  icon: const Icon(Icons.share),
                  label: const Text('Share'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF4A90E2),
                    side: const BorderSide(color: Color(0xFF4A90E2)),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy),
                  label: const Text('Copy'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF4A90E2),
                    side: const BorderSide(color: Color(0xFF4A90E2)),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _shareResultsAsJson,
                  icon: const Icon(Icons.file_download),
                  label: const Text('JSON'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
