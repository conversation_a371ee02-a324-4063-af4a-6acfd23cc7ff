import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:aiclipper_whisper/aiclipper_whisper.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

class WhisperTab extends StatefulWidget {
  const WhisperTab({super.key});

  @override
  State<WhisperTab> createState() => _WhisperTabState();
}

class _WhisperTabState extends State<WhisperTab> with TickerProviderStateMixin {
  String _platformVersion = 'Unknown';
  String _transcriptionResult = '';
  String? _selectedAudioPath;
  String _selectedFileName = '';
  bool _isTranscribing = false;
  bool _isModelReady = false;
  bool _hasSelectedFile = false;
  bool _isTestingFFI = false;
  String _ffiTestResult = '';
  bool _showMetrics = false;

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  final _aiclipperWhisperPlugin = AiclipperWhisper();

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    initPlatformState();
    _prepareWhisperModel();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _setupAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _prepareWhisperModel() async {
    try {
      await _getMultilingualModelPath();
      setState(() {
        _isModelReady = true;
      });
      debugPrint("Whisper model prepared successfully.");
    } catch (e) {
      debugPrint("Error preparing whisper model: $e");
      setState(() {
        _transcriptionResult =
            "Error preparing whisper model. Please ensure the model file is available.";
      });
    }
  }

  Future<void> initPlatformState() async {
    String platformVersion;
    try {
      platformVersion =
          await _aiclipperWhisperPlugin.getPlatformVersion() ?? 'Unknown';
    } on PlatformException {
      platformVersion = 'Unknown';
    }

    if (!mounted) return;

    setState(() {
      _platformVersion = platformVersion;
    });
  }

  Future<void> _pickAudioFile() async {
    try {
      // Clear temporary files if possible
      if (Platform.isIOS || Platform.isAndroid) {
        try {
          await FilePicker.platform.clearTemporaryFiles();
        } catch (e) {
          debugPrint('Warning: Could not clear temporary files: $e');
        }
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['wav', 'mp3', 'm4a', 'aac', 'flac', 'ogg'],
        allowMultiple: false,
        withData: false,
        withReadStream: false,
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        final file = File(result.files.single.path!);

        if (!await file.exists()) {
          _showErrorSnackBar('Selected file is not accessible.');
          return;
        }

        setState(() {
          _selectedAudioPath = result.files.single.path!;
          _selectedFileName = result.files.single.name;
          _hasSelectedFile = true;
          _transcriptionResult = '';
        });

        _showSuccessSnackBar('Audio file selected successfully!');
      }
    } on PlatformException catch (e) {
      _showErrorSnackBar('Error selecting file: ${e.message}');
    } catch (e) {
      _showErrorSnackBar('Error selecting file: $e');
    }
  }

  Future<void> _shareTranscription() async {
    if (_transcriptionResult.isNotEmpty) {
      await Share.share(_transcriptionResult, subject: 'Whisper Transcription');
    }
  }

  Future<void> _copyToClipboard() async {
    if (_transcriptionResult.isNotEmpty) {
      await Clipboard.setData(ClipboardData(text: _transcriptionResult));
      _showSuccessSnackBar('Copied to clipboard!');
    }
  }

  Future<void> _testFFI() async {
    setState(() {
      _isTestingFFI = true;
      _ffiTestResult = '';
    });

    try {
      final ffiTest = await AiclipperWhisper.testFFI();

      setState(() {
        _ffiTestResult = '''
FFI Test Results:
• Available: ${ffiTest['available']}
• Platform: ${ffiTest['platform']}
• iOS: ${ffiTest['ios']}
• Error: ${ffiTest['error'] ?? 'None'}
        ''';
        _isTestingFFI = false;
      });

      if (ffiTest['available'] == true) {
        _showSuccessSnackBar('✅ FFI is working!');
      } else {
        _showErrorSnackBar('❌ FFI not available: ${ffiTest['error']}');
      }
    } catch (e) {
      setState(() {
        _ffiTestResult = 'Error testing FFI: $e';
        _isTestingFFI = false;
      });
      _showErrorSnackBar('FFI test failed: $e');
    }
  }

  void _toggleMetrics() {
    setState(() {
      _showMetrics = !_showMetrics;
    });
  }

  void _clearMetrics() {
    AiclipperWhisper.clearMetrics();
    setState(() {});
    _showSuccessSnackBar('Metrics cleared!');
  }

  Future<void> _runPerformanceTest({required bool useFFI}) async {
    if (_selectedAudioPath == null || !_isModelReady) {
      _showErrorSnackBar('Please select an audio file first.');
      return;
    }

    setState(() {
      _isTranscribing = true;
      _transcriptionResult = '';
    });

    _pulseController.repeat(reverse: true);

    try {
      final modelPath = await _getMultilingualModelPath();

      final transcription = await AiclipperWhisper.transcribeAudio(
        modelPath: modelPath,
        audioPath: _selectedAudioPath!,
        useFFI: useFFI,
      );

      setState(() {
        _transcriptionResult = transcription;
        _isTranscribing = false;
      });

      _pulseController.stop();
      _pulseController.reset();

      final method = useFFI ? 'FFI' : 'Method Channel';
      _showSuccessSnackBar('✅ $method test completed successfully!');

      // Refresh metrics display if it's showing
      if (_showMetrics) {
        setState(() {});
      }
    } catch (e) {
      setState(() {
        _transcriptionResult = 'Performance test failed: $e';
        _isTranscribing = false;
      });

      _pulseController.stop();
      _pulseController.reset();

      final method = useFFI ? 'FFI' : 'Method Channel';
      _showErrorSnackBar('❌ $method test failed. Please try again.');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  Future<String> _getMultilingualModelPath() async {
    final cacheDir = await getApplicationCacheDirectory();
    final modelFile = File('${cacheDir.path}/ggml-base.bin');

    if (!await modelFile.exists()) {
      debugPrint("Model not found in cache, copying from assets...");
      final byteData = await rootBundle.load('assets/models/ggml-base.bin');
      final bytes = byteData.buffer.asUint8List();
      await modelFile.writeAsBytes(bytes);
      debugPrint("Model copied to ${modelFile.path}");
    }

    return modelFile.path;
  }

  String _buildMetricsText() {
    final summary = AiclipperWhisper.getPerformanceSummary();

    if ((summary['totalTranscriptions'] as int? ?? 0) == 0) {
      return '📊 No transcriptions performed yet.\n\nRun some transcriptions to see performance metrics!';
    }

    final ffi = summary['ffi'] as Map<String, dynamic>? ?? {};
    final channel = summary['methodChannel'] as Map<String, dynamic>? ?? {};
    final StringBuffer buffer = StringBuffer();

    buffer.writeln('🚀 PERFORMANCE METRICS');
    buffer.writeln('=' * 40);
    buffer.writeln('Total transcriptions: ${summary['totalTranscriptions']}');
    buffer.writeln();

    // FFI Performance
    buffer.writeln('📊 FFI Performance:');
    final ffiCount = ffi['count'] as int? ?? 0;
    if (ffiCount > 0) {
      buffer.writeln('  ✅ Count: $ffiCount');
      buffer.writeln(
          '  📈 Success Rate: ${((ffi['successRate'] as double? ?? 0) * 100).toStringAsFixed(1)}%');
      buffer.writeln(
          '  ⏱️  Avg Duration: ${(ffi['avgDurationMs'] as double? ?? 0).toStringAsFixed(1)}ms');
      buffer.writeln(
          '  📊 Median Duration: ${(ffi['medianDurationMs'] as double? ?? 0).toStringAsFixed(1)}ms');
      buffer.writeln(
          '  🚀 Avg Real-time Ratio: ${(ffi['avgRealTimeRatio'] as double? ?? 0).toStringAsFixed(2)}x');
    } else {
      buffer.writeln('  ❌ No FFI transcriptions performed.');
    }

    buffer.writeln();

    // Method Channel Performance
    buffer.writeln('📱 Method Channel Performance:');
    final channelCount = channel['count'] as int? ?? 0;
    if (channelCount > 0) {
      buffer.writeln('  ✅ Count: $channelCount');
      buffer.writeln(
          '  📈 Success Rate: ${((channel['successRate'] as double? ?? 0) * 100).toStringAsFixed(1)}%');
      buffer.writeln(
          '  ⏱️  Avg Duration: ${(channel['avgDurationMs'] as double? ?? 0).toStringAsFixed(1)}ms');
      buffer.writeln(
          '  📊 Median Duration: ${(channel['medianDurationMs'] as double? ?? 0).toStringAsFixed(1)}ms');
      buffer.writeln(
          '  🚀 Avg Real-time Ratio: ${(channel['avgRealTimeRatio'] as double? ?? 0).toStringAsFixed(2)}x');
    } else {
      buffer.writeln('  ❌ No Method Channel transcriptions performed.');
    }

    // Performance Comparison
    if (ffiCount > 0 && channelCount > 0) {
      final ffiAvg = ffi['avgDurationMs'] as double? ?? 1;
      final channelAvg = channel['avgDurationMs'] as double? ?? 1;
      final speedup = channelAvg / ffiAvg;

      buffer.writeln();
      buffer.writeln('⚡ PERFORMANCE COMPARISON:');
      if (speedup > 1) {
        buffer.writeln(
            '  🎯 FFI is ${speedup.toStringAsFixed(2)}x FASTER than Method Channel');
      } else {
        buffer.writeln(
            '  ⚠️  FFI is ${(1 / speedup).toStringAsFixed(2)}x SLOWER than Method Channel');
      }
      buffer.writeln('  📊 FFI avg: ${ffiAvg.toStringAsFixed(1)}ms');
      buffer.writeln('  📊 Channel avg: ${channelAvg.toStringAsFixed(1)}ms');
    }

    // Recent transcriptions
    buffer.writeln();
    buffer.writeln('📝 Recent Transcriptions:');
    final recent = summary['recentMetrics'] as List? ?? [];
    if (recent.isNotEmpty) {
      for (final metric in recent.take(5)) {
        final method = metric['method'] ?? 'Unknown';
        final durationMs =
            (metric['durationMs'] as double? ?? 0).toStringAsFixed(1);
        final realTimeRatio =
            (metric['realTimeRatio'] as double? ?? 0).toStringAsFixed(2);
        final success = (metric['success'] as bool? ?? false) ? '✅' : '❌';
        buffer
            .writeln('  $success $method: ${durationMs}ms (${realTimeRatio}x)');
      }
    } else {
      buffer.writeln('  No recent transcriptions.');
    }

    buffer.writeln();
    buffer.writeln('💡 Tip: Run transcriptions with both FFI and');
    buffer.writeln('   Method Channel to compare performance!');

    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20.0),
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height -
                  MediaQuery.of(context).padding.top -
                  MediaQuery.of(context).padding.bottom -
                  40, // Account for padding
            ),
            child: IntrinsicHeight(
              child: Column(
                children: [
                  // Header
                  _buildHeader(),
                  const SizedBox(height: 30),

                  // Main Action Area
                  _buildMainActionArea(),
                  const SizedBox(height: 30),

                  // FFI Test Results (if available)
                  if (_ffiTestResult.isNotEmpty) ...[
                    _buildFFITestSection(),
                    const SizedBox(height: 30),
                  ],

                  // Performance Metrics Section
                  _buildMetricsSection(),
                  const SizedBox(height: 30),

                  // Results Section
                  _buildResultsSection(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            children: [
              const Icon(
                Icons.mic_external_on,
                size: 50,
                color: Colors.white,
              ),
              const SizedBox(height: 12),
              const Text(
                'AI Speech Transcription',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Powered by Whisper AI • $_platformVersion',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white70,
                ),
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isModelReady ? Icons.check_circle : Icons.hourglass_empty,
                    color: _isModelReady ? Colors.green : Colors.orange,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isModelReady
                        ? 'Ready to transcribe'
                        : 'Preparing model...',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
          // FFI Test Button
          Positioned(
            top: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconButton(
                onPressed: _isTestingFFI ? null : _testFFI,
                icon: _isTestingFFI
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Icon(
                        Icons.science,
                        color: Colors.white,
                        size: 20,
                      ),
                tooltip: 'Test FFI',
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainActionArea() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // File Selection
          if (!_hasSelectedFile) ...[
            Icon(
              Icons.upload_file,
              size: 60,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            const Text(
              'Select an audio file to begin',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Supports MP3, M4A, WAV, AAC, FLAC, OGG',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _isModelReady ? _pickAudioFile : null,
              icon: const Icon(Icons.folder_open),
              label: const Text('Choose Audio File'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF667eea),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 5,
              ),
            ),
          ] else ...[
            // Selected File Display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.audiotrack, color: Colors.green),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _selectedFileName,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                        const Text(
                          'Ready to transcribe',
                          style: TextStyle(
                            color: Colors.green,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => setState(() => _hasSelectedFile = false),
                    icon: const Icon(Icons.close, color: Colors.grey),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),

            // Transcription Method Selection
            const Text(
              'Choose Transcription Method',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Compare performance between FFI and Method Channel',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),

            // FFI vs Channel Buttons
            Row(
              children: [
                Expanded(
                  child: _buildTranscriptionButton(
                    title: 'FFI',
                    subtitle: 'Fast & Direct',
                    icon: Icons.rocket_launch,
                    color: const Color(0xFF34C759),
                    onPressed: _isTranscribing
                        ? null
                        : () => _runPerformanceTest(useFFI: true),
                    isActive: _isTranscribing,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildTranscriptionButton(
                    title: 'Channel',
                    subtitle: 'Standard',
                    icon: Icons.swap_horiz,
                    color: const Color(0xFF007AFF),
                    onPressed: _isTranscribing
                        ? null
                        : () => _runPerformanceTest(useFFI: false),
                    isActive: _isTranscribing,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Change File Button
            TextButton.icon(
              onPressed: _pickAudioFile,
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('Choose Different File'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTranscriptionButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback? onPressed,
    required bool isActive,
  }) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isActive ? _pulseAnimation.value : 1.0,
          child: Container(
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: onPressed == null
                    ? [Colors.grey[300]!, Colors.grey[400]!]
                    : [color, color.withValues(alpha: 0.8)],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: onPressed == null
                  ? []
                  : [
                      BoxShadow(
                        color: color.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onPressed,
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isActive) ...[
                        const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ),
                      ] else ...[
                        Icon(
                          icon,
                          size: 28,
                          color: Colors.white,
                        ),
                      ],
                      const SizedBox(height: 6),
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 11,
                          color: Colors.white70,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFFITestSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(Icons.science, color: Color(0xFF667eea)),
              const SizedBox(width: 12),
              const Text(
                'FFI Test Results',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Flexible(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: SingleChildScrollView(
                child: _isTestingFFI
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text(
                              'Testing FFI...\nThis may take a few moments.',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : SelectableText(
                        _ffiTestResult,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.5,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricsSection() {
    final summary = AiclipperWhisper.getPerformanceSummary();
    final hasMetrics = (summary['totalTranscriptions'] as int? ?? 0) > 0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Performance Metrics',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                ],
              ),
              if (hasMetrics) ...[
                Row(
                  children: [
                    TextButton(
                      onPressed: _clearMetrics,
                      child: Text(
                        'Clear',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: _toggleMetrics,
                      child: Text(
                        _showMetrics ? 'Hide' : 'Show',
                        style: const TextStyle(
                          color: Color(0xFF007AFF),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          if (!hasMetrics) ...[
            const SizedBox(height: 12),
            Text(
              'Run transcriptions with both methods to see performance comparison',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ] else ...[
            if (_showMetrics) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: SelectableText(
                    _buildMetricsText(),
                    style: const TextStyle(
                      fontSize: 12,
                      height: 1.4,
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ),
            ] else ...[
              const SizedBox(height: 12),
              _buildQuickMetricsSummary(summary),
            ],
          ],
        ],
      ),
    );
  }

  Widget _buildQuickMetricsSummary(Map<String, dynamic> summary) {
    final ffi = summary['ffi'] as Map<String, dynamic>? ?? {};
    final channel = summary['methodChannel'] as Map<String, dynamic>? ?? {};

    final ffiCount = ffi['count'] as int? ?? 0;
    final channelCount = channel['count'] as int? ?? 0;

    return Row(
      children: [
        if (ffiCount > 0) ...[
          Expanded(
            child: _buildQuickMetric(
              'FFI',
              '${(ffi['avgDurationMs'] as double? ?? 0).toStringAsFixed(0)}ms avg',
              Colors.green,
            ),
          ),
        ],
        if (ffiCount > 0 && channelCount > 0) ...[
          const SizedBox(width: 12),
        ],
        if (channelCount > 0) ...[
          Expanded(
            child: _buildQuickMetric(
              'Channel',
              '${(channel['avgDurationMs'] as double? ?? 0).toStringAsFixed(0)}ms avg',
              Colors.blue,
            ),
          ),
        ],
        if (ffiCount > 0 && channelCount > 0) ...[
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              'FFI ${((channel['avgDurationMs'] as double? ?? 1) / (ffi['avgDurationMs'] as double? ?? 1)).toStringAsFixed(1)}x faster',
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: Colors.orange,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildQuickMetric(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_transcriptionResult.isEmpty && !_isTranscribing) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        minHeight: 200,
        maxHeight: MediaQuery.of(context).size.height *
            0.4, // Max 40% of screen height
      ),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(Icons.text_snippet, color: Color(0xFF667eea)),
              const SizedBox(width: 12),
              const Text(
                'Transcription Result',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_transcriptionResult.isNotEmpty) ...[
                IconButton(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy, size: 20),
                  tooltip: 'Copy to clipboard',
                ),
                IconButton(
                  onPressed: _shareTranscription,
                  icon: const Icon(Icons.share, size: 20),
                  tooltip: 'Share transcription',
                ),
              ],
            ],
          ),
          const SizedBox(height: 16),
          Flexible(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: SingleChildScrollView(
                child: _isTranscribing
                    ? const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CircularProgressIndicator(),
                            SizedBox(height: 16),
                            Text(
                              'Processing your audio...\nThis may take a few moments.',
                              textAlign: TextAlign.center,
                              style: TextStyle(color: Colors.grey),
                            ),
                          ],
                        ),
                      )
                    : SelectableText(
                        _transcriptionResult,
                        style: const TextStyle(
                          fontSize: 16,
                          height: 1.5,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
